# 🚀 正式环境跨域问题解决方案

## 📋 问题描述

- **本地环境**: `http://localhost/simplifiedreader` - 同域，无跨域问题
- **正式环境**: `https://ebook.dutp.cn` - 跨域，触发安全限制

## 🎯 解决步骤

### 步骤1：在阅读器页面添加PostMessage支持

在 `https://ebook.dutp.cn` 的阅读器页面中添加以下代码：

#### 方法A：直接在HTML中添加

在阅读器页面的 `<head>` 或 `<body>` 底部添加：

```html
<script src="/path/to/reader-postmessage-integration.js"></script>
```

#### 方法B：内联代码

直接在阅读器页面中添加以下代码：

```javascript
// PostMessage通信支持
(function() {
    let parentOrigin = null;
    
    // 允许的父页面域名
    const allowedOrigins = [
        'http://localhost:3000',
        'http://localhost:8080', 
        'http://localhost:81',
        'https://your-admin-domain.com', // 替换为你的管理后台域名
    ];
    
    // 消息监听器
    window.addEventListener('message', function(event) {
        if (!allowedOrigins.includes(event.origin)) {
            console.warn('未授权的消息来源:', event.origin);
            return;
        }
        
        if (!parentOrigin) {
            parentOrigin = event.origin;
            console.log('已连接到父页面:', parentOrigin);
        }
        
        console.log('收到父页面消息:', event.data);
        
        // 处理消息
        if (event.data && event.data.type) {
            switch (event.data.type) {
                case 'parent_ready':
                    // 回复ready消息
                    window.parent.postMessage({
                        type: 'iframe_ready',
                        message: 'iframe已准备就绪',
                        timestamp: new Date().toISOString()
                    }, event.origin);
                    break;
                    
                case 'init_data':
                    console.log('收到初始化数据:', event.data);
                    break;
                    
                case 'SCROLL_TO_ELEMENT':
                    // 处理滚动请求
                    const domId = event.data.payload?.domId;
                    if (domId) {
                        const element = document.querySelector(`[data-toc-id="${domId}"], #${domId}`);
                        if (element) {
                            element.scrollIntoView({ behavior: 'smooth' });
                        }
                    }
                    break;
                    
                case 'PRINT':
                    // 处理打印请求
                    window.print();
                    break;
            }
        }
    });
    
    // 页面加载完成后发送ready消息
    function sendReady() {
        window.parent.postMessage({
            type: 'iframe_ready',
            message: 'iframe页面加载完成',
            timestamp: new Date().toISOString()
        }, '*');
    }
    
    // 延迟发送ready消息
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => setTimeout(sendReady, 1000));
    } else {
        setTimeout(sendReady, 1000);
    }
    
    // 全局错误处理
    window.addEventListener('error', function(event) {
        window.parent.postMessage({
            type: 'iframe_error',
            error: event.message,
            timestamp: new Date().toISOString()
        }, '*');
    });
    
    console.log('PostMessage通信已初始化');
})();
```

### 步骤2：验证父页面配置

确保父页面（管理后台）的origin验证包含正确的域名：

```javascript
const allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:8080',
    'http://localhost:81',
    'http://localhost',
    'https://ebook.dutp.cn'  // ✅ 已添加
]
```

### 步骤3：检查服务器配置

确保 `https://ebook.dutp.cn` 的服务器没有设置阻止iframe嵌入的头部：

#### 检查这些HTTP头部：

```
X-Frame-Options: DENY          ❌ 会阻止iframe
X-Frame-Options: SAMEORIGIN    ❌ 只允许同域iframe
X-Frame-Options: ALLOW-FROM    ✅ 可以指定允许的域名
```

#### 推荐的服务器配置：

**Nginx:**
```nginx
# 允许特定域名嵌入iframe
add_header X-Frame-Options "ALLOW-FROM https://your-admin-domain.com";

# 或者移除X-Frame-Options限制
# add_header X-Frame-Options "";
```

**Apache:**
```apache
# 允许特定域名嵌入iframe
Header always set X-Frame-Options "ALLOW-FROM https://your-admin-domain.com"
```

### 步骤4：测试验证

#### 本地测试：
1. 修改 `.env.development` 中的 `VITE_READER_PREVIEW_URL` 指向正式环境
2. 观察控制台是否有跨域错误

#### 正式环境测试：
1. 部署管理后台到正式环境
2. 测试iframe通信是否正常

## 🔧 调试方法

### 在浏览器控制台中：

```javascript
// 检查连接状态
window.debugIframe.getStatus()

// 发送测试消息
window.debugIframe.sendTestMessage()

// 在iframe中检查（如果有调试代码）
window.postMessageDebug.getStatus()
```

### 检查网络请求：

1. 打开开发者工具 → Network 标签
2. 查看iframe请求是否成功加载
3. 检查响应头中的 `X-Frame-Options`

## ⚠️ 常见问题

### 1. 仍然报跨域错误
- 检查阅读器页面是否正确添加了PostMessage代码
- 确认服务器没有设置 `X-Frame-Options: DENY`

### 2. iframe加载失败
- 检查 `https://ebook.dutp.cn` 是否可以正常访问
- 确认URL参数格式正确

### 3. 消息发送失败
- 检查origin验证配置
- 确认消息格式正确

## 📞 技术支持

如果问题仍然存在，请提供：

1. 浏览器控制台的完整错误信息
2. Network 标签中iframe请求的详细信息
3. 阅读器页面的响应头信息

## 🎉 成功标志

当一切正常工作时，你应该看到：

1. ✅ 控制台显示 "已连接到父页面"
2. ✅ 调试信息显示 "iframe状态: ✅ 已连接"
3. ✅ 没有跨域相关的错误信息
4. ✅ 可以正常发送和接收PostMessage
