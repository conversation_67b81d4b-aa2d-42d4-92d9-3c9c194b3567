# PostMessage 跨域通信集成指南

## 概述

本指南说明如何在阅读器页面中集成postMessage通信，以解决跨域访问问题。

## 已实现的功能

### 1. 父页面 (approvePage.vue) 功能

- ✅ 设置postMessage监听器
- ✅ 处理来自iframe的各种消息类型
- ✅ 向iframe发送消息
- ✅ 错误处理和安全验证
- ✅ 生命周期管理

### 2. 支持的消息类型

#### 从iframe到父页面的消息：
- `iframe_ready`: iframe准备就绪
- `content_loaded`: 内容加载完成
- `iframe_error`: iframe发生错误
- `user_interaction`: 用户交互事件
- `heartbeat`: 心跳消息

#### 从父页面到iframe的消息：
- `parent_ready`: 父页面准备就绪
- `init_data`: 初始化数据
- `update_content`: 更新内容

## 在阅读器页面中的实现

### 1. 在阅读器页面添加postMessage支持

在你的阅读器页面 (`VITE_READER_PREVIEW_URL` 指向的页面) 中添加以下代码：

```javascript
// 在页面加载完成后设置postMessage监听器
window.addEventListener('load', function() {
    // 设置消息监听器
    window.addEventListener('message', handleParentMessage, false);
    
    // 发送就绪消息
    sendMessageToParent({
        type: 'iframe_ready',
        message: 'iframe已准备就绪',
        timestamp: new Date().toISOString()
    });
});

// 处理来自父页面的消息
function handleParentMessage(event) {
    // 验证消息来源
    const allowedOrigins = [
        'http://localhost:3000',
        'http://localhost:8080',
        'http://localhost:81',
        // 添加你的实际域名
    ];
    
    if (!allowedOrigins.includes(event.origin)) {
        console.warn('收到来自未授权域的消息:', event.origin);
        return;
    }
    
    console.log('收到父页面消息:', event.data);
    
    // 处理不同类型的消息
    switch (event.data.type) {
        case 'parent_ready':
            console.log('父页面已准备就绪');
            break;
            
        case 'init_data':
            console.log('收到初始化数据:', event.data);
            // 使用初始化数据设置页面
            initializeWithData(event.data);
            break;
            
        default:
            console.log('未知消息类型:', event.data.type);
    }
}

// 向父页面发送消息
function sendMessageToParent(data) {
    try {
        window.parent.postMessage(data, '*');
        console.log('向父页面发送消息:', data);
    } catch (error) {
        console.error('发送消息到父页面失败:', error);
    }
}

// 使用初始化数据设置页面
function initializeWithData(data) {
    if (data.bookId) {
        console.log('书籍ID:', data.bookId);
    }
    if (data.chapterId) {
        console.log('章节ID:', data.chapterId);
    }
    if (data.auditType) {
        console.log('审核类型:', data.auditType);
    }
}

// 在内容加载完成后发送消息
function onContentLoaded() {
    sendMessageToParent({
        type: 'content_loaded',
        message: '内容加载完成',
        timestamp: new Date().toISOString()
    });
}

// 在发生错误时发送消息
function onError(error) {
    sendMessageToParent({
        type: 'iframe_error',
        error: error.message || '未知错误',
        timestamp: new Date().toISOString()
    });
}

// 在用户交互时发送消息
function onUserInteraction(action) {
    sendMessageToParent({
        type: 'user_interaction',
        action: action,
        timestamp: new Date().toISOString()
    });
}
```

### 2. 错误处理

在阅读器页面中添加全局错误处理：

```javascript
// 捕获JavaScript错误
window.addEventListener('error', function(event) {
    sendMessageToParent({
        type: 'iframe_error',
        error: event.message,
        filename: event.filename,
        lineno: event.lineno,
        timestamp: new Date().toISOString()
    });
});

// 捕获Promise rejection
window.addEventListener('unhandledrejection', function(event) {
    sendMessageToParent({
        type: 'iframe_error',
        error: event.reason,
        timestamp: new Date().toISOString()
    });
});
```

## 测试方法

### 1. 使用示例页面测试

访问 `/iframe-communication-example.html` 来测试postMessage通信功能。

### 2. 在开发环境中测试

1. 确保 `VITE_READER_PREVIEW_URL` 配置正确
2. 在浏览器开发者工具中查看控制台消息
3. 验证消息的发送和接收

### 3. 调试技巧

```javascript
// 在父页面中添加调试信息
console.log('iframe URL:', href.value);
console.log('iframe origin:', new URL(href.value).origin);

// 在iframe页面中添加调试信息
console.log('parent origin:', event.origin);
console.log('received message:', event.data);
```

## 安全注意事项

1. **验证消息来源**: 始终验证 `event.origin`
2. **限制允许的域**: 只允许信任的域发送消息
3. **验证消息格式**: 验证接收到的消息格式和内容
4. **避免敏感信息**: 不要通过postMessage传递敏感信息

## 常见问题

### Q: 消息发送失败怎么办？
A: 检查iframe是否加载完成，目标origin是否正确。

### Q: 如何确保消息的可靠传递？
A: 实现消息确认机制和重试逻辑。

### Q: 如何处理多个iframe的情况？
A: 为每个iframe分配唯一标识符，在消息中包含标识符。

## 快速集成到现有阅读器代码

### 1. 导入工具函数

在你的阅读器组件中添加导入：

```javascript
// 导入 PostMessage 处理工具
import {
  setupPostMessageListener,
  sendPageLoadedMessage,
  sendContentLoadedMessage,
  sendErrorMessage,
  sendUserInteractionMessage,
  getConnectionStatus,
  setupGlobalErrorHandling,
  handleScrollToElement,
  handlePrintRequest,
  handleInitData
} from '@/utils/postMessageHandler.js';
```

### 2. 替换现有的消息监听器

将你现有的这些代码：

```javascript
window.addEventListener('message', function(event) {
  if (event.data && event.data.type === 'SCROLL_TO_ELEMENT') {
    // 现有逻辑
  }
}, false);

window.addEventListener('message', function (event) {
  if (event.data.type === 'PRINT') {
    // 现有逻辑
  }
});
```

替换为：

```javascript
// PostMessage 处理函数
const postMessageHandlers = {
  handleScrollToElement: (data) => {
    handleScrollToElement(data, highlightKeyWordSmooth);
  },

  handlePrintRequest: () => {
    handlePrintRequest(PAGE_ITEMS_CONTAINER_ID, printJS);
  },

  handleInitData: (data) => {
    handleInitData(data);
  }
};

// 在 onMounted 中设置
onMounted(() => {
  // 现有代码...

  // 设置 PostMessage 通信
  setupPostMessageListener(postMessageHandlers);
  setupGlobalErrorHandling();

  // 发送页面加载完成消息
  sendPageLoadedMessage();

  // 现有代码...
});
```

### 3. 添加状态跟踪（可选）

```javascript
// 添加状态变量
const connectionStatus = ref({ isConnected: false, parentOrigin: null });

// 定期更新连接状态
const statusInterval = setInterval(() => {
  connectionStatus.value = getConnectionStatus();
}, 1000);

// 清理定时器
onBeforeUnmount(() => {
  clearInterval(statusInterval);
});
```

### 4. 添加用户交互跟踪（可选）

在你的滚动和点击事件中添加：

```javascript
const scrollCallback = throttle(() => {
  // 发送滚动交互消息
  sendUserInteractionMessage('page_scroll', {
    scrollTop: document.querySelector(`#${PAGE_ITEMS_CONTAINER_ID}`).scrollTop,
    currentPage: store.currentPageIndex
  });

  // 现有的滚动逻辑...
}, 400);
```

### 5. 添加错误处理

在你的错误处理中添加：

```javascript
.catch((error) => {
  sendErrorMessage(error);
  // 现有错误处理...
});
```

## 完整示例

参考 `src/examples/SimplifiedReaderWithPostMessage.vue` 文件查看完整的集成示例。

## 下一步

1. ✅ 已创建postMessage工具函数 (`src/utils/postMessageHandler.js`)
2. ✅ 已创建完整的集成示例 (`src/examples/SimplifiedReaderWithPostMessage.vue`)
3. 在实际的阅读器页面中按照上述步骤集成postMessage支持
4. 根据具体需求调整消息类型和处理逻辑
5. 测试跨域通信功能
