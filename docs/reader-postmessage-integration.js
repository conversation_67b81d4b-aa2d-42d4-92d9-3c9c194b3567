/**
 * 阅读器页面PostMessage集成代码
 * 将此代码添加到 https://ebook.dutp.cn 的阅读器页面中
 */

(function() {
    'use strict';
    
    let parentOrigin = null;
    let isConnected = false;
    
    // 允许的父页面域名
    const allowedOrigins = [
        'http://localhost:3000',
        'http://localhost:8080',
        'http://localhost:81',
        'https://your-domain.com', // 替换为你的实际域名
        // 添加更多允许的域名
    ];
    
    console.log('🚀 PostMessage通信模块已加载');
    
    // 设置消息监听器
    window.addEventListener('message', function(event) {
        console.log('📨 收到父页面消息:', event);
        
        // 验证消息来源
        if (!allowedOrigins.includes(event.origin)) {
            console.warn('⚠️ 收到来自未授权域的消息:', event.origin);
            return;
        }
        
        // 记录父页面origin
        if (!parentOrigin) {
            parentOrigin = event.origin;
            isConnected = true;
            console.log('✅ 已连接到父页面:', parentOrigin);
        }
        
        // 处理不同类型的消息
        if (event.data && event.data.type) {
            switch (event.data.type) {
                case 'parent_ready':
                    console.log('👋 父页面已准备就绪');
                    sendMessageToParent({
                        type: 'iframe_ready',
                        message: 'iframe已准备就绪',
                        timestamp: new Date().toISOString()
                    });
                    break;
                    
                case 'init_data':
                    console.log('📋 收到初始化数据:', event.data);
                    handleInitData(event.data);
                    break;
                    
                case 'SCROLL_TO_ELEMENT':
                    console.log('📍 滚动到元素:', event.data.payload);
                    handleScrollToElement(event.data.payload);
                    break;
                    
                case 'PRINT':
                    console.log('🖨️ 打印请求');
                    handlePrintRequest();
                    break;
                    
                case 'test_message':
                    console.log('🧪 收到测试消息:', event.data.message);
                    sendMessageToParent({
                        type: 'user_interaction',
                        action: 'test_message_received',
                        message: '测试消息已收到',
                        timestamp: new Date().toISOString()
                    });
                    break;
                    
                default:
                    console.log('❓ 未知消息类型:', event.data.type);
            }
        }
    }, false);
    
    // 向父页面发送消息
    function sendMessageToParent(data) {
        try {
            const targetOrigin = parentOrigin || '*';
            window.parent.postMessage(data, targetOrigin);
            console.log('📤 向父页面发送消息:', data);
        } catch (error) {
            console.error('❌ 发送消息失败:', error);
        }
    }
    
    // 处理初始化数据
    function handleInitData(data) {
        if (data.bookId) {
            console.log('📚 书籍ID:', data.bookId);
        }
        if (data.chapterId) {
            console.log('📖 章节ID:', data.chapterId);
        }
        if (data.auditType) {
            console.log('🔍 审核类型:', data.auditType);
        }
        
        // 发送确认消息
        sendMessageToParent({
            type: 'user_interaction',
            action: 'init_data_received',
            receivedData: {
                bookId: data.bookId,
                chapterId: data.chapterId,
                auditType: data.auditType
            },
            timestamp: new Date().toISOString()
        });
    }
    
    // 处理滚动到元素
    function handleScrollToElement(payload) {
        if (!payload || !payload.domId) return;
        
        const domId = payload.domId;
        let targetElement = document.querySelector(
            `:is(h1, h2, h3, h4, h5, h6)[data-toc-id="${domId}"]`
        );
        
        if (!targetElement) {
            targetElement = document.querySelector(`div[id="${domId}"]`);
        }
        
        if (targetElement) {
            targetElement.scrollIntoView({ behavior: 'smooth' });
            
            // 高亮元素（如果有highlightKeyWordSmooth函数）
            if (typeof highlightKeyWordSmooth === 'function') {
                highlightKeyWordSmooth(targetElement);
            }
            
            // 发送滚动完成消息
            sendMessageToParent({
                type: 'user_interaction',
                action: 'scroll_to_element',
                domId: domId,
                timestamp: new Date().toISOString()
            });
        }
    }
    
    // 处理打印请求
    function handlePrintRequest() {
        try {
            // 如果页面有特定的打印逻辑，在这里实现
            if (typeof window.print === 'function') {
                window.print();
            }
            
            // 发送打印完成消息
            sendMessageToParent({
                type: 'user_interaction',
                action: 'print_completed',
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('❌ 打印失败:', error);
            sendMessageToParent({
                type: 'iframe_error',
                error: '打印失败: ' + error.message,
                timestamp: new Date().toISOString()
            });
        }
    }
    
    // 发送错误消息
    function sendErrorMessage(error) {
        sendMessageToParent({
            type: 'iframe_error',
            error: error.message || error,
            timestamp: new Date().toISOString()
        });
    }
    
    // 页面加载完成后发送ready消息
    function sendReadyMessage() {
        sendMessageToParent({
            type: 'iframe_ready',
            message: 'iframe页面加载完成',
            url: window.location.href,
            timestamp: new Date().toISOString()
        });
    }
    
    // 设置全局错误处理
    window.addEventListener('error', function(event) {
        sendErrorMessage({
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
        });
    });
    
    window.addEventListener('unhandledrejection', function(event) {
        sendErrorMessage({
            message: 'Promise rejection: ' + event.reason,
            type: 'unhandledrejection'
        });
    });
    
    // 页面加载完成后自动发送ready消息
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(sendReadyMessage, 1000);
        });
    } else {
        setTimeout(sendReadyMessage, 1000);
    }
    
    // 定期发送心跳消息
    setInterval(function() {
        if (isConnected) {
            sendMessageToParent({
                type: 'heartbeat',
                timestamp: new Date().toISOString()
            });
        }
    }, 30000); // 每30秒发送一次心跳
    
    // 暴露一些调试方法到全局
    window.postMessageDebug = {
        sendTest: function() {
            sendMessageToParent({
                type: 'user_interaction',
                action: 'debug_test',
                message: '调试测试消息',
                timestamp: new Date().toISOString()
            });
        },
        getStatus: function() {
            return {
                connected: isConnected,
                parentOrigin: parentOrigin,
                currentUrl: window.location.href
            };
        },
        sendReady: sendReadyMessage
    };
    
    console.log('✅ PostMessage通信模块初始化完成');
    console.log('🔧 调试方法: window.postMessageDebug');
    
})();
