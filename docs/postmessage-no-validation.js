/**
 * PostMessage通信 - 无验证版本（仅用于测试）
 * ⚠️ 警告：此版本不验证消息来源，仅用于调试跨域问题
 */

// 处理来自iframe的消息（无验证版本）
function handlePostMessage(event) {
  console.log('📨 收到iframe消息:', event.data, '来源:', event.origin)
  
  // ⚠️ 注意：这里没有origin验证，仅用于测试
  
  // 根据消息类型处理不同的操作
  switch (event.data.type) {
    case 'iframe_ready':
      console.log('✅ iframe已准备就绪')
      iframeLoaded.value = true
      // 发送队列中的消息
      processMessageQueue()
      // 向iframe发送初始化数据
      setTimeout(() => {
        sendMessageToIframe({
          type: 'init_data',
          bookId: bookId.value,
          chapterId: chapterId.value,
          auditType: auditType.value
        })
      }, 100)
      break
      
    case 'iframe_error':
      console.error('❌ iframe发生错误:', event.data.error)
      MessagePlugin.error('预览页面加载失败')
      break
      
    case 'content_loaded':
      console.log('📄 内容加载完成')
      break

    case 'user_interaction':
      console.log('👆 用户交互:', event.data.action)
      break
      
    case 'heartbeat':
      console.log('💓 收到iframe心跳')
      break
      
    default:
      console.log('❓ 未知消息类型:', event.data.type)
  }
}

/**
 * 阅读器页面PostMessage代码 - 无验证版本
 * 将此代码添加到阅读器页面中进行测试
 */
const readerPostMessageCode = `
(function() {
    console.log('🚀 PostMessage通信模块已加载（无验证版本）');
    
    // 消息监听器（无验证）
    window.addEventListener('message', function(event) {
        console.log('📨 收到父页面消息:', event.data, '来源:', event.origin);
        
        // ⚠️ 注意：这里没有origin验证，仅用于测试
        
        // 处理不同类型的消息
        if (event.data && event.data.type) {
            switch (event.data.type) {
                case 'parent_ready':
                    console.log('👋 父页面已准备就绪');
                    sendMessageToParent({
                        type: 'iframe_ready',
                        message: 'iframe已准备就绪',
                        timestamp: new Date().toISOString()
                    });
                    break;
                    
                case 'init_data':
                    console.log('📋 收到初始化数据:', event.data);
                    break;
                    
                case 'SCROLL_TO_ELEMENT':
                    console.log('📍 滚动到元素:', event.data.payload);
                    const domId = event.data.payload?.domId;
                    if (domId) {
                        const element = document.querySelector('[data-toc-id="' + domId + '"], #' + domId);
                        if (element) {
                            element.scrollIntoView({ behavior: 'smooth' });
                        }
                    }
                    break;
                    
                case 'PRINT':
                    console.log('🖨️ 打印请求');
                    window.print();
                    break;
                    
                case 'test_message':
                    console.log('🧪 收到测试消息:', event.data.message);
                    sendMessageToParent({
                        type: 'user_interaction',
                        action: 'test_message_received',
                        message: '测试消息已收到',
                        timestamp: new Date().toISOString()
                    });
                    break;
                    
                default:
                    console.log('❓ 未知消息类型:', event.data.type);
            }
        }
    }, false);
    
    // 向父页面发送消息
    function sendMessageToParent(data) {
        try {
            window.parent.postMessage(data, '*'); // 使用通配符
            console.log('📤 向父页面发送消息:', data);
        } catch (error) {
            console.error('❌ 发送消息失败:', error);
        }
    }
    
    // 页面加载完成后发送ready消息
    function sendReady() {
        sendMessageToParent({
            type: 'iframe_ready',
            message: 'iframe页面加载完成',
            timestamp: new Date().toISOString()
        });
    }
    
    // 延迟发送ready消息
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => setTimeout(sendReady, 1000));
    } else {
        setTimeout(sendReady, 1000);
    }
    
    // 全局错误处理
    window.addEventListener('error', function(event) {
        sendMessageToParent({
            type: 'iframe_error',
            error: event.message,
            timestamp: new Date().toISOString()
        });
    });
    
    // 暴露调试方法
    window.postMessageDebug = {
        sendTest: function() {
            sendMessageToParent({
                type: 'user_interaction',
                action: 'debug_test',
                message: '调试测试消息',
                timestamp: new Date().toISOString()
            });
        },
        getStatus: function() {
            return {
                connected: true,
                currentUrl: window.location.href
            };
        },
        sendReady: sendReady
    };
    
    console.log('✅ PostMessage通信模块初始化完成（无验证版本）');
    console.log('🔧 调试方法: window.postMessageDebug');
    
})();
`;

// 如何使用这个无验证版本：
console.log('📋 使用说明：');
console.log('1. 将上面的 readerPostMessageCode 添加到阅读器页面');
console.log('2. 在父页面中使用无验证的 handlePostMessage 函数');
console.log('3. 测试完成后，记得恢复origin验证！');
