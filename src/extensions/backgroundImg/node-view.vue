<template>
    <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-bg-view" @mouseenter="showChild"
        @mouseleave="cannelHideLayer">
        <div class="umo-node-container umo-node-bg" :style="`min-height: ${node.attrs.height}px`">
            <div>
                <div v-show="mousemove" class="top-node-mu" @click.stop="keepOpen">
                    <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
                        <EllipsisIcon />
                        <template #dropdown>
                            <t-dropdown-item :value="2">
                                <div style="display: flex; align-items: center" @click="imageEdit">
                                    <SettingIcon />
                                    <span style="margin-left: 5px">{{
                                        t('insert.image.setting')
                                    }}</span>
                                </div>
                            </t-dropdown-item>
                            <t-dropdown-item :value="2">
                                <div style="display: flex; align-items: center" @click="changeDragerState">
                                    <DragMoveIcon v-if="!node.attrs.isDrager" />
                                    <CloseRectangleIcon v-else />
                                    <span style="margin-left: 5px">{{
                                        !node.attrs.isDrager ? t('insert.backgroundImg.openDrag') :
                                            t('insert.backgroundImg.noDrag')
                                    }}</span>
                                </div>
                            </t-dropdown-item>
                            <t-dropdown-item :value="5">
                                <div style="display: flex; align-items: center" @click="handleDelNode">
                                    <DeleteIcon />
                                    <span style="margin-left: 5px">{{
                                        t('insert.image.remove')
                                    }}</span>
                                </div>
                            </t-dropdown-item>
                        </template>
                    </t-dropdown>
                </div>
                <div class="node-view-image-body" :style="`min-height: ${node.attrs.height}px`">
                    <img v-show="node.attrs.src && !node.attrs.isDrager" ref="imageRef" class="node-view-image" :style="{
                        width: node.attrs.width + 'px',
                        height: node.attrs.height + 'px',
                        left: node.attrs.left + 'px',
                        top: node.attrs.top + 'px',
                        zIndex: -1
                    }" :src="node.attrs.src" :data-id="node.attrs.id" object-fit="contain" @load="onLoad" />
                    <div v-if="node.attrs.src && node.attrs.isDrager" class="node-view-image-mask"
                        :style="`width: ${contentW}px; height: ${contentH}px`"></div>
                    <node-view-content ref="contentRef" :contenteditable="!node.attrs.isDrager" :style="styled" />
                </div>

            </div>
            <drager v-if="node.attrs.src && node.attrs.isDrager" :selected="selected" :rotatable="false"
                :boundary="false" :draggable="true" :width="Number(node.attrs.width)"
                :height="Number(node.attrs.height)" :left="Number(node.attrs.left)" :top="Number(node.attrs.top)"
                :min-width="14" :min-height="14" :max-width="maxWidth" :z-index="10"
                :equal-proportion="node.attrs.equalProportion" :check-collision="true" @resize="onResize"
                @click="selected = true" @drag-end="onDragEnd" @drag-start="onDragStart" @drag="onDrag">

                <!--  -->
                <div class="node-view-image-body">
                    <img ref="imageRef" :src="node.attrs.src" :data-id="node.attrs.id" loading="lazy"
                        object-fit="contain" @load="onLoad" />
                </div>
            </drager>

        </div>
        <modal v-model:visible="imageEditPopup" attach="body" :header="t('insert.backgroundImg.text')" width="850"
            :close-on-overlay-click="false" @confirm="onSubmit" @opened="onOpen" @closed="onClose" @cancel="onClose">
            <div style="overflow: hidden;">
                <t-form :data="formData" :label-width="120">
                    <div>
                        <t-form :data="formData" label-align="top">
                            <div class="layoutGrid">
                                <div class="layout_item">
                                    <t-form-item :label="t('insert.backgroundImg.uploadText')" name="text">
                                        <t-button @click="uploadImg">{{ t('insert.backgroundImg.uploadText')
                                            }}</t-button>
                                    </t-form-item>
                                </div>
                                <div v-if="node.attrs.src" class="layout_item">
                                    <img :src="fileData.fileUrl || node.attrs.src" style="width: 200px;height: auto;" />
                                </div>
                            </div>

                            <div class="layoutProportion">
                                <div class="layoutProportion-title">{{
                                    t('insert.backgroundImg.backgroundEqualProportion') }}
                                </div>
                                <div>
                                    <t-form-item name="equalProportion">
                                        <t-switch v-model="formData.equalProportion"
                                            @change="doChangeProportion" /><span style="padding-left:10px;">{{
                                                formData.equalProportion ?
                                                    t('insert.backgroundImg.equalProportion') :
                                                    t('insert.backgroundImg.freeProportionally') }}</span>
                                    </t-form-item>
                                </div>
                            </div>

                            <div class="layout_padding">
                                <div class="layout_padding_header">
                                    {{ t('insert.backgroundImg.paddingHeader') }}
                                </div>

                                <div class="layout_padding-bg-icon">
                                    <t-tooltip :content="t('insert.block.tip')" :overlay-style="{ width: '200px' }"
                                        placement="right" show-arrow>
                                        <div class="raduis-bg-icon" @click="paddingClick">
                                            <LinkIcon size="30" :color="paddingLinkValue ? '#1890ff' : ''" />
                                        </div>
                                    </t-tooltip>
                                </div>



                                <div class="layout_padding_item">
                                    <t-form-item :label="t('insert.backgroundImg.paddingTop')" name="paddingTop">
                                        <t-input-number v-model="formData.paddingTop" />
                                    </t-form-item>
                                </div>
                                <div class="layout_padding_item">
                                    <t-form-item :label="t('insert.backgroundImg.paddingRight')" name="paddingRight">
                                        <t-input-number v-model="formData.paddingRight" />
                                    </t-form-item>
                                </div>
                                <div class="layout_padding_item">
                                    <t-form-item :label="t('insert.backgroundImg.paddingBottom')" name="paddingBottom">
                                        <t-input-number v-model="formData.paddingBottom" />
                                    </t-form-item>
                                </div>
                                <div class="layout_padding_item">
                                    <t-form-item :label="t('insert.backgroundImg.paddingLeft')" name="paddingLeft">
                                        <t-input-number v-model="formData.paddingLeft" />
                                    </t-form-item>
                                </div>

                            </div>

                        </t-form>
                    </div>
                </t-form>
            </div>
        </modal>
    </node-view-wrapper>


</template>

<script setup lang="ts">
import { NodeViewContent, nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import Drager from 'es-drager'
import {
    CloseRectangleIcon, DeleteIcon,
    DragMoveIcon, EllipsisIcon, LinkIcon, SettingIcon,
} from 'tdesign-icons-vue-next'

import { useMouseOver } from '@/hooks/mouseOver'
import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor, imageViewer, getCaptionStyle, captionStyle } =
    useStore()
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()

const { isLoading, error } = useImage({ src: node.attrs.src })
const imageEditPopup = ref(false)
const containerRef: any = ref(null)
const formData = ref({})
const imageRef = $ref<HTMLImageElement | null>(null)
let selected = $ref(false)
let maxWidth = $ref(0)
let maxHeight = $ref(0)
const contentRef = ref(null)
const changeDragerState = () => {
    updateAttributes({
        isDrager: !node.attrs.isDrager,
    })
}

const handleDelNode = () => {
    deleteNode()
}

const contentW = computed(() => contentRef.value?.$el.clientWidth || 1000)
const contentH = computed(() => contentRef.value?.$el.clientHeight || 30)
// 开启dialog
const imageEdit = () => {
    imageEditPopup.value = true
    fileData.value = {

    }
    formData.value = {
        paddingTop: node.attrs.paddingTop,
        paddingRight: node.attrs.paddingRight,
        paddingBottom: node.attrs.paddingBottom,
        paddingLeft: node.attrs.paddingLeft,
        equalProportion: node.attrs.equalProportion,
    }
    paddingLinkValue.value = node.attrs.linkage
    watch(() => formData.value.paddingTop, (value) => {
        if (paddingLinkValue.value) {
            formData.value.paddingLeft = value
            formData.value.paddingRight = value
            formData.value.paddingBottom = value
        }

    })

}

const onClose = () => {
    imageEditPopup.value = false
}

const onLoad = async () => {
    const test = {
        width: imageRef?.naturalWidth,
        height: imageRef?.naturalHeight
    }
    const newRatio = test.width / test.height
    const newHeight = 697 / newRatio
    const { clientWidth = 1, clientHeight = 1 } = imageRef ?? {}
    maxWidth = containerRef.value?.$el.clientWidth
    const ratio = clientWidth / clientHeight
    maxHeight = containerRef.value?.$el.clientWidth / ratio
    if (node.attrs.height === null) {

        updateAttributes({ width: maxWidth > 697 ? 697 : maxWidth, height: newHeight.toFixed(2) })
    }

}

const onRotate = ({ angle }: { angle: number }) => {
    updateAttributes({ angle })
}
const onResize = ({ width, height }: { width: number; height: number }) => {
    updateAttributes({
        width: width.toFixed(2),
        height: height.toFixed(2),
    })
}


const formValidatorStatus = ref(null)
const rules = reactive({
    imageTitle: [
        {
            required: true,
            message: t('insert.image.imagePlaceholder'),
            type: 'error',
            trigger: 'blur',
        },
    ],
})

const onSubmit = () => {
    if (!node.attrs.src) {
        useMessage('error', t('insert.backgroundImg.uploadImg'))
        return
    }

    updateAttributes({
        src: fileData.value.fileUrl || node.attrs.src,
        size: fileData.value.fileSize,
        imageTitle: fileData.value.originName,
        name: fileData.value.originName,
        paddingTop: formData.value.paddingTop,
        paddingRight: formData.value.paddingRight,
        paddingBottom: formData.value.paddingBottom,
        paddingLeft: formData.value.paddingLeft,
        equalProportion: formData.value.equalProportion,
        linkage: paddingLinkValue.value
    })
    imageEditPopup.value = false

}

const onDrag = ({ left, top }: { left: number; top: number }) => {
    updateAttributes({ left, top })
}
const dragStartTop = ref(0)
const dragStartLeft = ref(0)
const onDragStart = ({ top, left }) => {
    console.log("onDragStart", top, left, node.attrs.top, node.attrs.left, Number(node.attrs.left) + Number(node.attrs.width), containerRef.value?.$el.clientWidth)

    dragStartTop.value = Number(node.attrs.top) < 0 || Number(top) < 0 ? 0 : Number(node.attrs.top)
    const leftNum = Number(node.attrs.left);

    if (leftNum < 0 || Number(left) < 0) {
        dragStartLeft.value = 0
    } else if (leftNum + Number(node.attrs.width) >= containerRef.value?.$el.clientWidth) {
        dragStartLeft.value = containerRef.value?.$el.clientWidth - Number(node.attrs.width)
    } else {
        dragStartLeft.value = leftNum
    }
    console.log("dragStartLeft", dragStartLeft.value, dragStartTop.value)
}
const onDragEnd = ({ top, left }) => {
    console.log('onDragEnd', top, left)
    let f = true;
    if (Number(top) < 0) {
        f = false;
    }
    if (Number(left) < 0) {
        f = false
    } else if (Number(left) + Number(node.attrs.width) >= containerRef.value?.$el.clientWidth) {
        f = false
    }
    if (!f) {
        updateAttributes({ left: dragStartLeft.value, top: dragStartTop.value })
    } else {
        updateAttributes({ left, top })
    }

}
onClickOutside(containerRef, () => {
    selected = false
})


const paddingLinkValue = ref(node.attrs.linkage)
const paddingClick = () => {
    paddingLinkValue.value = !paddingLinkValue.value

    watch(() => formData.value.paddingTop, (value) => {
        if (paddingLinkValue.value) {
            formData.value.paddingLeft = value
            formData.value.paddingRight = value
            formData.value.paddingBottom = value
        }

    })

    if (paddingLinkValue.value) return useMessage('success', '内边距已联动')
    if (!paddingLinkValue.value) return useMessage('error', '内边距已取消联动')

}

const styled = computed(() => {
    return `padding:${node.attrs.paddingTop}px ${node.attrs.paddingRight}px ${node.attrs.paddingBottom}px ${node.attrs.paddingLeft}px;`
})


const fileData = ref({})
// 上传图片
const uploadImg = () => {
    chooseFile((file) => {
        fileData.value = file
    }, {
        optPreChekck: defaultOptPreChekck
    })
}

</script>

<style lang="less" scoped>
.umo-node-bg-view {
    .umo-node-bg {
        max-width: 100%;
        width: auto;
        position: relative;
        z-index: 20;

        .top-node-mu {
            position: absolute;
            right: 0px;
            top: 0px;
            z-index: 99;
            cursor: pointer;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 4px;
            color: #fff;
            padding: 5px;
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .node-view-image-body {
            width: 100%;
            height: 100%;
            position: relative;

            .node-view-image {
                position: absolute;
                top: 0;
                left: 0;
            }

            .node-view-image-mask {
                position: absolute;
                top: 0;
                left: 0;
            }

        }

        &:not(.is-draggable) .es-drager {
            max-width: 100%;
            max-height: 100%;
        }

        img {
            display: block;
            max-width: 100%;
            max-height: 100%;
            width: 100%;
            height: 100%;
        }

        .title {
            margin: 5px 0;

            width: 100%;
            text-align: center;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--umo-text-color-light);
            font-size: 12px;
            gap: 10px;

            .loading-icon {
                color: var(--umo-primary-color);
                font-size: 22px;
                animation: turn 1s linear infinite;
            }
        }

        .error {
            width: 200px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: var(--umo-text-color-light);
            font-size: 12px;

            .error-icon {
                font-size: 72px;
                margin: -8px 0 -2px;
            }
        }
    }
}

@keyframes turn {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes progress {
    0% {
        width: 0;
    }

    100% {
        width: 100%;
    }
}

.layoutGrid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 10px;
    align-items: center;

}

.layout_Setting {
    margin-top: 20px;
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 10px;
    position: relative;
    font-weight: bold;

    .layout_header_title {
        position: absolute;
        top: -10px;
        left: 10px;
        background-color: #fff;
        padding: 0 10px;
    }
}

.layout_padding {
    margin-top: 20px;
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 10px;
    position: relative;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 20px;
    font-weight: bold;

    .layout_padding_header {
        position: absolute;
        top: -10px;
        left: 10px;
        background-color: #fff;
        padding: 0 10px;
        color: #333;
    }

    .layout_padding-bg-icon {
        position: absolute;
        right: 0px;
        top: 35%;
        transform: rotate(135deg);
        cursor: pointer;
    }

}

.layoutProportion {
    margin-top: 20px;
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 10px;
    position: relative;
    font-weight: bold;

    .layoutProportion-title {
        position: absolute;
        top: -10px;
        left: 10px;
        background-color: #fff;
        padding: 0 10px;
        color: #333;
    }
}
</style>