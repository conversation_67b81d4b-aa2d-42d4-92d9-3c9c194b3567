import { type CommandProps, mergeAttributes, Node } from '@tiptap/core'
import { type CommandProps, VueNodeViewRenderer } from '@tiptap/vue-3'

import { BACKGROUNDIMG } from '../page/node-names'
import NodeView from './node-view.vue'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setBackgroundImg: {
      setBackgroundImg: (options: any, replace?: any) => ReturnType
    }
  }
}
export default Node.create({
  name: BACKGROUNDIMG,
  group: 'block',
  content: 'block+',
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },
      src: {
        default: null,
      },
      width: {
        default: 697,
      },
      height: {
        default: null,
      },
      left: {
        default: 0,
      },
      top: {
        default: 0,
      },
      // 是否等比例缩放
      equalProportion: {
        default: true,
      },
      paddingLeft: {
        default: 0,
      },
      paddingRight: {
        default: 0,
      },
      paddingTop: {
        default: 0,
      },
      paddingBottom: {
        default: 0,
      },
      isDrager: {
        default: false,
      },
      justification: {
        default: 'leftTop',
      },
      linkage: {
        default: false,
      },
      imageTitle: {
        default: '',
      },
    }
  },
  parseHTML() {
    return [{ tag: BACKGROUNDIMG }]
  },
  renderHTML({ HTMLAttributes }) {
    return [BACKGROUNDIMG, mergeAttributes(HTMLAttributes), 0]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView)
  },
  addCommands() {
    return {
      setBackgroundImg:
        (options: {
          src: string
          paddingLeft: number
          paddingRight: number
          paddingTop: number
          paddingBottom: number
          justification: string
          equalProportion: boolean
          linkage: boolean
        }) =>
        ({ commands, editor, tr, dispatch }: CommandProps) => {
          console.log('setBackgroundImg', options)
          if (dispatch) {
            if (options?.imageTitle) {
              tr.replaceRangeWith(
                editor.state.selection.anchor,
                editor.state.selection.anchor,
                editor.schema.node(
                  this.name,
                  {
                    ...options,
                  },
                  editor.schema.nodes.paragraph.create(
                    '',
                    editor.schema.text(options.imageTitle || ''),
                  ),
                ),
              )
            } else {
              tr.replaceRangeWith(
                editor.state.selection.anchor,
                editor.state.selection.anchor,
                editor.schema.node(
                  this.name,
                  {
                    ...options,
                  },
                  editor.schema.nodes.paragraph.create(),
                ),
              )
            }
          }
          return true
        },
    }
  },
})
