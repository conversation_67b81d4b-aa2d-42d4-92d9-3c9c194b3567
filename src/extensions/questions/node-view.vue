<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view-rc" @mouseenter="showChild"
    @mouseleave="cannelHideLayer">
    <div class="questions-wrapper">
      <div v-show="mousemove" class="top-node-mu" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="2">
              <div style="display: flex; align-items: center" @click="editQuestions($event, node)">
                <FileSearchIcon />
                <!-- 编辑 -->
                <span style="margin-left: 5px">{{
                  t('insert.questions.editText')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="3">
              <div style="display: flex; align-items: center" @click="delQuestions">
                <DeleteIcon />
                <span style="margin-left: 5px">{{
                  t('insert.questions.delBtnText')
                }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>
      <div class="questions-content">
        <div v-for="(item, index) in localQuestionList" :key="index" class="question-item">
          <!-- 填空题 -->
          <template-questions-onview-fillinBlanksQuestion v-if="
            item.userQuestion &&
            isFillinBlanksQuestion(item.userQuestion.questionType)
          " :data="formatToQuestionData(item)" :config="{
            hasAnswer: false,
            questionTypeShow: item.questionTypeShow,
          }" />
          <!-- 判断题内容 -->
          <template-questions-onview-trueOrFalseQuestion v-if="
            item.userQuestion &&
            isTrueOrFalseQuestion(item.userQuestion.questionType)
          " :data="formatToQuestionData(item)" :config="{
            hasAnswer: false,
            questionTypeShow: item.questionTypeShow,
          }" />
          <!-- 单选题、多选题内容 -->

          <template-questions-onview-optionSelectQuestion v-if="item.userQuestion &&
            isOptionSelectQuestionType(item.userQuestion.questionType)
          " :data="formatToQuestionData(item)" :config="{
              hasAnswer: false,
              questionTypeShow: item.questionTypeShow,
            }" />
          <!-- 排序题内容 -->
          <template-questions-onview-sortingQuestion v-if="
            item.userQuestion &&
            isSortingQuestion(item.userQuestion.questionType)
          " :data="formatToQuestionData(item)" :config="{
            hasAnswer: false,
            questionTypeShow: item.questionTypeShow,
          }" />
          <!-- 简答题内容 -->
          <template-questions-onview-answerInShortQuestion v-if="
            item.userQuestion &&
            isDescriptiveAnswerQuestion(item.userQuestion.questionType)
          " :data="formatToQuestionData(item)" :config="{
            hasAnswer: false,
            questionTypeShow: item.questionTypeShow,
          }" />
          <!-- 连线题 -->
          <template-questions-onview-matchingQuestion v-if="
            item.userQuestion &&
            isMatchingQuestions(item.userQuestion.questionType)
          " :data="formatToQuestionData(item)" :left-option="formatToLeftOption(item)"
            :right-option="formatToRightOption(item)" :matching-result="formatToMatchResult(item)" :config="{
              hasAnswer: false,
              questionTypeShow: item.questionTypeShow,
            }" />
          <!-- 编程题 -->
          <template-questions-onview-programmingQuestion v-if="isProgrammingQuestion(item.userQuestion?.questionType)"
            :data="formatToQuestionData(item)" :config="{
              hasAnswer: false,
              questionTypeShow: item.questionTypeShow,
            }" />
        </div>
      </div>
    </div>
  </node-view-wrapper>
  <!-- 编辑: 要node-view-wrapper定义在封套外面 -->
  <t-drawer v-model:visible="showQuestionsEditPopup" attach="body" size="80%" mode="overlay" placement="right"
    :confirm-btn="t('insert.questions.update')" class="new-question-drawer" :header="t('editor.updateQuestionItem')"
    :destroy-on-close="true" :close-on-overlay-click="false" :on-confirm="updataQuestionsDataList"
    :on-close-btn-click="closeEditPopup">
    <CommonQuestionComp v-if="showQuestionsEditPopup" :question-list="tmpLocalQuestionList"
      :visibility="showQuestionsEditPopup" :is-update="true" :book-id="route.query.bookId"
      :chapter-id="route.query.chapterId" @update-params="handleParamsUpdate" />
  </t-drawer>
</template>

<script setup>
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import {
  DeleteIcon,
  EllipsisIcon,
  FileSearchIcon,
} from 'tdesign-icons-vue-next'
import { useRoute } from 'vue-router'

import { updateQuestion } from '@/api/book/bookQuestion'
import CommonQuestionComp from '@/components/menus/toolbar/insert/components/questions/commonQuestionComp.vue'
import { useMouseOver } from '@/hooks/mouseOver'
import {
  deriveBookQuestionObjectWithMinimumProperty,
  deriveUserQuestionObjectWithMinimumProperty,
  isDescriptiveAnswerQuestion,
  isFillinBlanksQuestion,
  isMatchingQuestions,
  isOptionSelectQuestionType,
  isProgrammingQuestion,
  isSortingQuestion,
  isTrueOrFalseQuestion,
} from '@/utils/questionTypeUtil'
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()

const route = useRoute()
const { node, updateAttributes } = defineProps(nodeViewProps)
const { editor } = useStore()
let showQuestionsEditPopup = $ref(false)
let localQuestionList = $ref([])
let tmpLocalQuestionList = []
onMounted(() => {
  const { questionsList } = node.attrs
  localQuestionList = questionsList
  console.log('localQuestionList')
})

function formatToQuestionData(userQuestionItem) {
  const options =
    userQuestionItem.userQuestion?.options?.map((opt) => {
      return {
        optionId: opt.optionId,
        optionContent: opt.optionContent,
        optionPosition: opt.optionPosition,
        rightFlag: opt.rightFlag,
      }
    }) ?? []
  return {
    showAnswer: false,
    questionType: userQuestionItem.userQuestion?.questionType,
    questionContent: userQuestionItem.userQuestion?.questionContent,
    analysis: userQuestionItem.userQuestion?.analysis,
    codeContent: userQuestionItem.userQuestion?.codeContent,
    questionRemark: userQuestionItem.userQuestion?.questionRemark || '',
    options,
    sectionReferTo: userQuestionItem.userQuestion.sectionReferTo ?? undefined,
  }
}
function formatToLeftOption(item) {
  const result =
    item?.userQuestion?.options?.filter(
      (qo) => `${qo.optionPosition}` === '1',
    ) ?? []
  return result
}
function formatToRightOption(item) {
  const result =
    item?.userQuestion.options?.filter(
      (qo) => `${qo.optionPosition}` === '2',
    ) ?? []
  return result
}
function formatToMatchResult(item) {
  let matchingResultJson = item.userQuestion.rightAnswer
  if (typeof matchingResultJson === 'string') {
    matchingResultJson = JSON.parse(matchingResultJson)
  }
  return matchingResultJson.map(({ source, target }) => {
    return { source, target }
  })
}
function handleParamsUpdate(newParams) {
  console.log('更新数据变更触发', newParams)
  // 实时获取试题编辑弹窗中试题的状态
  tmpLocalQuestionList = newParams.questionData
  tmpLocalQuestionList[0].questionTypeShow = newParams.questionTypeShow
  tmpLocalQuestionList[0].questionRemark =
    newParams.questionRemark || ''

  console.log('更新数据变更后', tmpLocalQuestionList)
}
function closeEditPopup() {
  showQuestionsEditPopup = false
}
function buildUserQuestionSaveParam(questionItem) {
  if (questionItem.questionType == 3) {
    const match = questionItem.questionContent?.match(/###(.*?)###/)
    if (match) {
      const contentBetweenHashes = match[1] // 获取 ### 和 ### 之间的内容

      // 判断内容是否为 HTML 标签
      const isHtmlTag =
        /^<([a-zA-Z][a-zA-Z0-9]*)\b[^>]*>.*<\/\1>$/.test(
          contentBetweenHashes,
        ) || /^<([a-zA-Z][a-zA-Z0-9]*)\b[^>]*\/?>$/.test(contentBetweenHashes) // 支持自闭合标签

      if (isHtmlTag) {
        //MessagePlugin.error('更新失败，题干中答案格式不正确，请使用纯文本')
        return false
      }
    } else {
      MessagePlugin.error('更新失败，题干中缺少答案')
    }
  }

  return {
    createSource: 1,
    codeContent: JSON.stringify({
      code: questionItem.codeContent.code || '',
      language: questionItem.codeContent.language || '',
    }),
    questionType: questionItem.questionType,
    questionContent: encodeURIComponent(questionItem.questionContent),
    questionRemark: questionItem.questionRemark,
    rightAnswer: questionItem.rightAnswer,
    analysis: encodeURIComponent(questionItem.analysis),
    disorder: questionItem.disorder,
    options: questionItem.options.map((opt) => {
      return {
        optionContent: encodeURIComponent(opt.optionContent),
        rightFlag: opt.rightFlag,
        optionPosition: opt.optionPosition ?? '',
      }
    }),
    sectionReferTo: questionItem.sectionReferTo ?? [],
  }
}
async function updataQuestionsDataList() {
  try {
    console.log(
      '更新题目的确认触发，此为tmpLocalQuestionList[0]',
      tmpLocalQuestionList,
    )

    const userQuestion = buildUserQuestionSaveParam(tmpLocalQuestionList[0])
    const updateParam = deriveBookQuestionObjectWithMinimumProperty(
      localQuestionList[0],
    )
    updateParam.userQuestion = userQuestion
    updateParam.userQuestionId = undefined

    if (!updateParam?.userQuestion) {
      MessagePlugin.error('更新失败，题干中答案格式不正确，请使用纯文本')
      return false
    }

    const updatedBookQuestion = await updateQuestion(updateParam)

    if (updatedBookQuestion.data) {
      const userQuestion = deriveUserQuestionObjectWithMinimumProperty(
        tmpLocalQuestionList[0],
      )

      console.log('更新后的userQuestion', userQuestion)
      // 只复制id部分到试题中并添加的到编辑器中，后台有可能返回解码后的题干和分析部分，而编辑器中保存的是编码后的题干和分析部分。
      if (
        isOptionSelectQuestionType(
          localQuestionList[0].userQuestion.questionType,
        ) ||
        isSortingQuestion(localQuestionList[0].userQuestion.questionType)
      ) {
        const minLength = Math.min(
          updateParam.userQuestion.options.length,
          updatedBookQuestion.data.userQuestion.options.length,
        )
        for (let i = 0; i < minLength; i++) {
          userQuestion.options[i].optionId =
            updatedBookQuestion.data.userQuestion.options[i].optionId
        }
      }
      const bookQuestion = deriveBookQuestionObjectWithMinimumProperty(
        localQuestionList[0],
      )
      bookQuestion.userQuestion = userQuestion
      bookQuestion.userQuestionId =
        updatedBookQuestion.data.userQuestion.questionId
      bookQuestion.userQuestion.sectionReferTo =
        tmpLocalQuestionList[0].sectionReferTo

      bookQuestion.questionTypeShow = tmpLocalQuestionList[0].questionTypeShow

      // 不用插入新的节点数据
      //  delQuestions()

      console.log('准备进行节点更新', bookQuestion)
      const nodeList = [bookQuestion]
      nodeList[0].questionTypeShow = tmpLocalQuestionList[0].questionTypeShow
      reCreateNewQuesionNode([bookQuestion])
      showQuestionsEditPopup = false
      MessagePlugin.success('更新成功')
    }
  } catch (err) {
    console.error(err)
  }
}
function reCreateNewQuesionNode(newQuestionList) {
  localQuestionList = newQuestionList
  console.log('节点更新中', newQuestionList)
  updateAttributes({ questionsList: newQuestionList })
  // editor?.value
  //   ?.chain()
  //   .focus()
  //   .questions({
  //     questionsList: newQuestionList,
  //   })
  //   .run()
}
// const toggleCollapse = () => {
//   collapsed.value = !collapsed.value
// }

function editQuestions(event, node) {
  tmpLocalQuestionList = localQuestionList.map(
    (question) => question.userQuestion,
  )

  // 从localQuestionList中获取questionTypeShow
  const questionTypeShow = localQuestionList[0]?.questionTypeShow || '1'
  tmpLocalQuestionList[0].questionTypeShow = questionTypeShow
  // 确保remark也被传递，如果为null则转换为空字符串
  tmpLocalQuestionList[0].questionRemark =
    localQuestionList[0]?.userQuestion?.questionRemark || ''

  console.log(
    'Data passed to edit popup (tmpLocalQuestionList[0]):',
    JSON.parse(JSON.stringify(tmpLocalQuestionList[0])),
  )

  showQuestionsEditPopup = true
}
function delQuestions() {
  editor.value?.commands.deleteSelectionNode() // addAttributes() 的 vnode:
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/_common.less';

.question-content-con {
  width: 100%;
  height: calc(100vh - 150px);
  .base-flex-column;
  justify-content: flex-start;
  align-items: center;
}

.umo-node-view-rc {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .questions-wrapper {
    width: 100%;
    position: relative;
    // padding: 5px;

    .top-node-mu {
      position: absolute;
      right: 10px;
      top: 20px;
      z-index: 99;
      font-size: 24px;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 4px;
      padding: 5px;
      color: #fff;
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .toggle-header {
      margin-left: 20px;
      margin-right: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;

      .short-img-con {
        display: flex;
        justify-content: center;
        align-items: center;

        .short-img-icon {
          width: 50px;
          height: 50px;
          margin-right: 20px;
        }
      }
    }

    .questions-content {
      width: 100%;

      .question-remark {
        width: 100%;
        margin-bottom: 12px;
        padding: 8px 12px;
        background-color: #f5f5f5;
        border-radius: 4px;

        .remark-label {
          color: #666;
          font-weight: 500;
        }

        .remark-content {
          color: #333;
          margin-left: 4px;
        }
      }

      .question-title-con {
        font-size: 30px;
        width: 100%;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 16px;
      }

      .question-item {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;


        .stem-con {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          align-items: center;
        }
      }
    }
  }

  // .short-questions-sty {
  //   background-color: rgb(224 240 241);
  //   border: 1px solid rgb(0 130 142);
  //   border-radius: 8px;
  // }
}

ul {
  list-style: none;
  padding: 0;
}

li {
  margin: 4px 0;
}

label {
  cursor: pointer;
}
</style>
<style lang="less">
.bellCss .umo-node-view-rc * {
  text-indent: 0;
}

.bellCss .umo-node-view-rc .question-item {
  .data-item {
    padding: 20px 10px;
    font-size: 14px;

    label {
      min-width: fit-content;
    }
  }
}
</style>
