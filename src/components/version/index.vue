<template>
    <t-dialog :visible="visible" @close="close" @open="openView" attach="body" :header="t('version.text')" width="800"
        :footer="false">
        <div class="version-content">
            <t-timeline>
                <t-timeline-item label="2025-08-20">
                    <div class="timeline-title">版本号:1.3.11</div>
                    <div class="timeline-custom-content">
                        新增行内公式图片上中位置调整方式，可以通过自己输入合适的值，进行上下位置的调整。默认值0，两行10px左右其他情况根据自身调整。</div>
                    <div class="timeline-custom-content">修复模板管理章头节头字体颜色配置</div>
                    <div class="timeline-custom-content">修复模板管理模板配置页UI样式</div>
                    <div class="timeline-custom-content">修复模板管理设置中页码位置以及样式</div>
                    <div class="timeline-custom-content">修复模板管理字体颜色与快捷方式统一</div>
                    <div class="timeline-custom-content">修复表格中多行多列合并产生的问题</div>
                    <div class="timeline-custom-content">修复字间距数值设置后，再次点击数值间距数值回显功能。</div>
                    <div class="timeline-custom-content">修复折叠组件内各个组件之间的合理间距</div>
                    <div class="timeline-custom-content">修复选中的文字再使用图标组件，图标组件成为文字背景图（只能选择一段文字，不支持多段）</div>
                </t-timeline-item>

                <t-timeline-item label="2025-08-13">
                    <div class="timeline-title">版本号:1.3.10</div>
                    <div class="timeline-custom-content">新增主题样式</div>
                    <div class="timeline-custom-content">新增版本管理模块</div>
                    <div class="timeline-custom-content">新增版式图片、行内图片、视频组件修改时，可以从资源库上传</div>
                    <div class="timeline-custom-content">新增气泡组件，其内容可设置为图片形式</div>
                    <div class="timeline-custom-content">修复背景图片显示问题</div>
                    <div class="timeline-custom-content">修复块组件UI布局问题,背景图片全部显示</div>
                    <div class="timeline-custom-content">修复下拉菜单中字号选项的排序出现混乱问题</div>
                    <div class="timeline-custom-content">修复表格标题字号默认大小问题</div>
                    <div class="timeline-custom-content">删除背景移除、背景设置选项</div>
                </t-timeline-item>

                <t-timeline-item label="2025-08-09">
                    <div class="timeline-title">版本号:1.3.9</div>
                    <div class="timeline-custom-content">新增折叠组件图标颜色选择</div>
                    <div class="timeline-custom-content">修复画廊对话框上移问题</div>
                    <div class="timeline-custom-content">修复图片名称一行显示。图片名边距减小。</div>
                    <div class="timeline-custom-content">修复连线题样式</div>
                    <div class="timeline-custom-content">修复画廊增加设置大小功能.画廊下边空白距离太大。</div>
                    <div class="timeline-custom-content">修复右侧滚动条样式</div>
                    <div class="timeline-custom-content">修复表格标题字号默认大小问题</div>
                </t-timeline-item>



            </t-timeline>
        </div>
    </t-dialog>
</template>

<script setup>

import { ref } from 'vue'

const visible = ref(false)

const openView = () => {
    console.log('123')
    visible.value = true
}

const close = () => {
    visible.value = false
}

defineExpose({ openView })

</script>


<style lang="less" scoped>
.version-content {
    padding: 20px;
    height: 500px;
    overflow-y: auto;

    &::-webkit-scrollbar {
        width: 0px;
    }

    .timeline-title {
        font-weight: bold;
        margin-bottom: 10px;
        background-color: #366ef4;
        color: #fff;
        padding: 5px 0px 5px 10px;
        border-radius: 4px;
    }

    .timeline-custom-content {
        line-height: 30px;
        color: #999;

        &::before {
            content: '•';
            margin-right: 10px;
            color: #333;
        }
    }
}
</style>
