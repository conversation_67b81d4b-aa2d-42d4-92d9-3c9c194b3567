<template>
  <div>
    <div class="back">
      <div class="back-btn-bg">
        <div class="back-btn" @click="$router.go(-1)">
          <ChevronLeftIcon />
          <span>返回</span>
        </div>
        <div class="back-title">
          当前节点：{{ auditType == 1 ? '章节提交' : '章节撤销申请' }}
        </div>
      </div>
      <div><t-button @click="audit">审批</t-button></div>
    </div>
    <div class="approvePage-main">
      <div class="approvePage-left">
        <TreeNodeApprove :data-list="chapterNodeList" />
      </div>
      <div class="approvePage-right">
        <div style="text-align: center; font-size: 30px; margin: 20px 0">
          {{ chapterName }}
        </div>
        <div v-if="revokedReason">撤销理由：{{ revokedReason }}</div>
<!--        <analysisJson check-tabs="desktop" :page-config-list="pageConfigList" />-->
        <iframe
          ref="iframeRef"
          :src="href"
          frameborder="0"
          class="iframePage"
          @load="onIframeLoad"
        ></iframe>
      </div>
    </div>
    <!-- 审核 -->
    <t-dialog
      v-model:visible="auditOpen"
      header="审核"
      placement="center"
      width="650px"
      :show-in-attached-element="true"
      @confirm="auditFormSubmit"
      @close="auditOpen = false"
    >
      <t-form
        ref="auditFormRef"
        :data="form"
        :rules="{
          chapterStatus: [
            {
              required: true,
              message: '审批结果',
              type: 'error',
              trigger: 'change',
            },
          ],
        }"
        label-width="100px"
      >
        <t-form-item label="审批结果" name="chapterStatus" required>
          <t-select v-model="form.chapterStatus" clearable style="width: 200px">
            <t-option :value="2" label="同意"></t-option>
            <t-option :value="3" label="驳回"></t-option>
          </t-select>
        </t-form-item>
        <t-form-item label="审批意见" name="remark">
          <t-textarea
            v-model="form.remark"
            placeholder="请输入审批意见"
            :maxlength="200"
            show-word-limit
            :autosize="{ minRows: 2, maxRows: 4 }"
          >
          </t-textarea>
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup name="approvePage">
import { ChevronLeftIcon } from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import { useRoute, useRouter } from 'vue-router'
import { auditChapter } from '@/api/book/bookChapterAuditLog.js'
import { getChapterContentInfo } from '@/api/chapterContent'
import { chapterCatalogList } from '@/api/book/chapter.js'
import TreeNodeApprove from '../components/TreeNodeApprove.vue'
const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const auditOpen = ref(false)
const auditType = ref('')
const revokedReason = ref('')
const pageConfigList = ref([])
const chapterNodeList = ref([])
const chapterName = ref('')
const chapterId = ref(null)
const bookId = ref(null)
const form = ref({
  logId: null,
})
const href = ref('')
const iframeRef = ref(null)
const iframeLoaded = ref(false)
const messageQueue = ref([]) // 消息队列，用于在iframe准备就绪前缓存消息

//#region 生命周期相关

onMounted(() => {
  chapterId.value = route.query.chapterId
  bookId.value = route.query.bookId
  form.value.logId = route.query.logId
  auditType.value = route.query.auditType
  revokedReason.value = route.query.revokedReason
  chapterName.value = route.query.chapterName
  getChapterDetail()
  chapterCatalogue()
  href.value = `${import.meta.env.VITE_READER_PREVIEW_URL + '?k='+ bookId.value + '&cid=' + chapterId.value + '&fromType=2' + '&operationFlag=false'}`;
  // href.value = `https://ebook.dutp.cn/reader?k=1912472480841334786&cid=1912478686301519874&cataid=1936349115415920641`;
  // 用于测试的简单页面
  // href.value = `/test-iframe-simple.html`;

  // 调试：打印实际的URL
  console.log('🔍 iframe URL:', href.value);
  console.log('🔍 VITE_READER_PREVIEW_URL:', import.meta.env.VITE_READER_PREVIEW_URL);
  console.log('🔍 当前页面URL:', window.location.href);

  // 设置postMessage监听器
  setupPostMessageListener()

  // 设置连接超时检测
  setTimeout(() => {
    if (!iframeLoaded.value) {
      console.warn('iframe连接超时，尝试重新建立连接')
      // 如果超时仍未连接，尝试直接发送消息
      if (iframeRef.value && iframeRef.value.contentWindow) {
        try {
          iframeRef.value.contentWindow.postMessage({
            type: 'parent_ready',
            message: '父页面准备就绪(超时重试)'
          }, '*')
        } catch (error) {
          console.error('重试连接失败:', error)
        }
      }
    }
  }, 5000)
})

onUnmounted(() => {
  // 清理postMessage监听器
  window.removeEventListener('message', handlePostMessage)
})

//#endregion

//#region postMessage通信相关

// 设置postMessage监听器
function setupPostMessageListener() {
  window.addEventListener('message', handlePostMessage, false)
}

// 处理来自iframe的消息
function handlePostMessage(event) {
  // 验证消息来源
  const allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:8080',
    'http://localhost:81',
    'http://localhost',
    'https://ebook.dutp.cn'
  ]

  // 动态添加当前配置的阅读器域名
  try {
    const readerOrigin = new URL(import.meta.env.VITE_READER_PREVIEW_URL).origin
    if (!allowedOrigins.includes(readerOrigin)) {
      allowedOrigins.push(readerOrigin)
    }
  } catch (e) {
    console.warn('无法解析VITE_READER_PREVIEW_URL:', e)
  }

  if (!allowedOrigins.includes(event.origin)) {
    console.warn('收到来自未授权域的消息:', event.origin)
    return
  }

  console.log('收到iframe消息:', event.data, '来源:', event.origin)

  // 根据消息类型处理不同的操作
  switch (event.data.type) {
    case 'iframe_ready':
      console.log('iframe已准备就绪')
      iframeLoaded.value = true
      // 发送队列中的消息
      processMessageQueue()
      // 向iframe发送初始化数据
      setTimeout(() => {
        sendMessageToIframe({
          type: 'init_data',
          bookId: bookId.value,
          chapterId: chapterId.value,
          auditType: auditType.value
        })
      }, 100)
      break

    case 'iframe_error':
      console.error('iframe发生错误:', event.data.error)
      MessagePlugin.error('预览页面加载失败')
      break

    case 'content_loaded':
      console.log('内容加载完成')
      break

    case 'user_interaction':
      console.log('用户交互:', event.data.action)
      break

    case 'heartbeat':
      // 心跳消息，用于检测连接状态
      console.log('收到iframe心跳')
      break

    default:
      console.log('未知消息类型:', event.data.type)
  }
}

// 向iframe发送消息
function sendMessageToIframe(data) {
  if (iframeRef.value && iframeLoaded.value) {
    try {
      let targetOrigin = '*'
      // 尝试解析URL获取origin
      if (href.value && href.value.startsWith('http')) {
        try {
          targetOrigin = new URL(href.value).origin
        } catch (urlError) {
          console.warn('URL解析失败，使用通配符origin:', urlError)
        }
      }
      iframeRef.value.contentWindow.postMessage(data, targetOrigin)
      console.log('向iframe发送消息:', data, '目标origin:', targetOrigin)
    } catch (error) {
      console.error('发送消息到iframe失败:', error)
      // 如果发送失败，可以尝试使用通配符origin
      try {
        iframeRef.value.contentWindow.postMessage(data, '*')
        console.log('使用通配符origin重新发送消息成功')
      } catch (retryError) {
        console.error('重试发送消息也失败:', retryError)
      }
    }
  } else {
    console.warn('iframe未准备就绪，无法发送消息')
    // 将消息加入队列，等待iframe准备就绪后发送
    messageQueue.value.push(data)
  }
}

// 处理消息队列
function processMessageQueue() {
  if (messageQueue.value.length > 0) {
    console.log(`处理队列中的 ${messageQueue.value.length} 条消息`)
    messageQueue.value.forEach(message => {
      sendMessageToIframe(message)
    })
    messageQueue.value = []
  }
}

// iframe加载完成事件
function onIframeLoad() {
  console.log('iframe加载完成')
  // 给iframe一些时间来设置消息监听器，然后尝试建立连接
  setTimeout(() => {
    // 先尝试发送父页面准备就绪消息
    if (iframeRef.value && iframeRef.value.contentWindow) {
      try {
        let targetOrigin = '*'
        // 尝试解析URL获取origin
        if (href.value && href.value.startsWith('http')) {
          try {
            targetOrigin = new URL(href.value).origin
          } catch (urlError) {
            console.warn('URL解析失败，使用通配符origin:', urlError)
          }
        }
        iframeRef.value.contentWindow.postMessage({
          type: 'parent_ready',
          message: '父页面已准备就绪'
        }, targetOrigin)
        console.log('直接发送父页面准备就绪消息成功，目标origin:', targetOrigin)
      } catch (error) {
        console.warn('直接发送消息失败，等待iframe发送ready消息:', error)
      }
    }
  }, 1000)
}

//#endregion

//#region 操作相关

// 获取章节详情
function getChapterDetail() {
  getChapterContentInfo(chapterId.value).then((response) => {
    const { data } = response
    if (data) {
      pageConfigList.value = JSON.parse(data.content).content || []
    } else {
      pageConfigList.value = []
    }
  })
}

// 获取章节目录
async function chapterCatalogue() {
  const res = await chapterCatalogList(chapterId.value)
  if (res.code === 200) {
    chapterNodeList.value = res.data
  }
}

// 审批
function audit() {
  form.value = {
    logId: form.value.logId,
    chapterStatus: null,
    remark: null,
  }
  auditOpen.value = true
}

// 提交审核
function auditFormSubmit() {
  proxy.$refs['auditFormRef']
    .validate({
      showErrorMessage: true,
    })
    .then((valid) => {
      if (valid === true) {
        auditChapter(form.value).then((res) => {
          MessagePlugin.success('审核成功')
          auditOpen.value = false
          router.push({
            path: '/pages/chapter',
          })
        })
      }
    })
}

// 调试方法 - 手动发送测试消息
function sendTestMessage() {
  // if (import.meta.env.DEV) {
    sendMessageToIframe({
      type: 'test_message',
      message: '这是一条测试消息',
      timestamp: new Date().toISOString()
    })
  // }
}

// 调试方法 - 重置iframe连接
function resetIframeConnection() {
  // if (import.meta.env.DEV) {
    iframeLoaded.value = false
    messageQueue.value = []
    console.log('iframe连接已重置')
    // 尝试重新建立连接
    setTimeout(() => {
      if (iframeRef.value && iframeRef.value.contentWindow) {
        iframeRef.value.contentWindow.postMessage({
          type: 'parent_ready',
          message: '父页面重新连接'
        }, '*')
        console.log('尝试重新建立连接')
      }
    }, 500)
  // }
}

// 暴露调试方法到全局 (仅开发环境)
// if (import.meta.env.DEV) {
  window.debugIframe = {
    sendTestMessage,
    resetIframeConnection,
    getStatus: () => ({
      loaded: iframeLoaded.value,
      queueLength: messageQueue.value.length,
      href: href.value
    })
  // }
}
//#endregion
</script>

<style lang="less" scoped>
.back {
  display: flex;
  width: 100%;
  padding-bottom: 20px;
  justify-content: space-between;
  border-bottom: 1px solid #f1f1f1;

  .back-btn-bg {
    display: flex;
    align-items: center;

    .back-title {
      margin-left: 60px;
      color: #666;
      font-size: 14px;
    }

    .back-btn {
      cursor: pointer;
      display: flex;
      align-items: center;
      color: #999;
      font-size: 14px;

      span {
        margin-left: 5px;
      }
    }
  }
}
/* 针对所有结构标签内的h1 */
:deep(h1) {
  font-size: 2em !important; /* 强制覆盖默认值 */
}

.approvePage-main {
  display: flex;

  .approvePage-left {
    width: 300px;

    background-color: #fff;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
    height: calc(100vh - 220px);
    border-right: 1px solid #ddd;
  }

  .approvePage-right {
    flex: 1;
    height: calc(100vh - 220px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
    background-color: #fafafa;
    .approvePage-page {
      width: 900px;
      margin: 0 auto;
      min-height: calc(100vh - 220px);
      background-color: #fff;
      padding: 20px;
      margin: 20px auto;
    }
    .iframePage {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
