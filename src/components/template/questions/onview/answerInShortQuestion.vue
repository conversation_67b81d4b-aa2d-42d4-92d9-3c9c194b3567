<template>
  <div class="data-item">
    <div v-if="localConfig?.questionTypeShow == 1" class="data-item-top">
      <slot name="selection" /><b>简答题</b>
    </div>
    <div class="data-item-em">
      <div v-if="data.questionRemark">
        <div v-html="data.questionRemark"></div>
      </div>
      <label v-if="props.config?.contentLabel"><b>题干：</b></label>
      <div v-html="data.questionContent"></div>
    </div>

    <div v-if="localConfig.hasAnswer" class="data-item-em">
      <label><b>参考答案：</b></label>
      <div v-html="data.rightAnswer"></div>
    </div>
    <div v-if="localConfig.hasAnswer" class="data-item-analysis">
      <label><b>解析：</b></label>
      <div style="margin-left: 10px;" v-html="data.analysis"></div>
    </div>
    <t-image-viewer :visible="visibleImg" :images="[imgUrl]" @close="closeViewer">
    </t-image-viewer>
  </div>
</template>
<script setup lang="ts">
import { useImageView } from '@/hooks/imageView'
const { visibleImg, imgUrl, closeViewer } = useImageView()
const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  config: {
    type: Object,
    // 引入showAnswer配置是因为的该组件也用在编辑器中，试题正确答案以及解析不需要打印出来。
    default: { contentLabel: false, hasAnswer: true }
  }
})
const defaultCfg = { contentLabel: false, hasAnswer: true, questionTypeShow: true }
let localConfig = $ref({
  ...defaultCfg,
  ...props.config
})
const rightAnswerBlanksContent = computed(() => {
  const input = props.data.questionContent
  return input.replace(/###([^#]+)###/g, "___$1___");
})
watch(
  () => props.config,
  (nCfg) => {
    if (nCfg) {
      localConfig = {
        ...defaultCfg,
        ...nCfg
      }
    }
  },
  {
    deep: true
  }
)
</script>
<style lang="less" scoped>
@import url('./question-data-item-style.less');
</style>
