<template>
  <div v-if="currentStep === 1" class="main-content">
    <t-card>
      <question-folder @folder-selected="handleFolderSelect" />
    </t-card>

    <t-card class="questions-list">
      <div class="filter-container" v-if="previewData.questions.length">
        <t-select
          v-model="filterForm.questionType"
          placeholder="题型筛选"
          clearable
          @change="handleFilter"
          class="filter-item"
        >
          <t-option
            v-for="item in questionTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </t-select>
        <t-input
          v-model="filterForm.keyword"
          placeholder="搜索题干关键字"
          clearable
          @change="handleFilter"
          class="filter-item"
        >
          <template #prefix-icon>
            <t-icon name="search" />
          </template>
        </t-input>
      </div>

      <div v-if="!filteredQuestions.length" class="empty-state">
        <t-empty description="暂时没有题目">
          <t-button theme="primary" @click="goToQuestionBank">
            去试题库添加
          </t-button>
        </t-empty>
      </div>

      <div v-else>
        <div
          v-for="(question, index) in filteredQuestions"
          :key="question.questionId"
          class="question-item"
        >
      
          <div class="question-header">
            <t-checkbox
              v-model="question.selected"
              @change="(val) => handleQuestionSelect(val, question)"
            > 
    
              <span class="question-type"
                >({{ getQuestionTypeName(question.questionType) }})</span
              >
            </t-checkbox>
          </div>
          <QuestionPreview :question="question" />
          <t-divider v-if="index !== filteredQuestions.length - 1" />
        </div>
      </div>
    </t-card>

    <t-card>
      <selected-questions
        :selected-questions="selectedQuestions"
        @remove-question="removeQuestion"
        @start-generate="startGenerate"
        :paper-type="paperType"
        @clear-all="clearAllQuestions"
      />
    </t-card>
  </div>

  <div v-if="currentStep === 2" class="paper-form">
    <div class="form-content">
      <t-form
        :data="paperForm"
        :rules="rules"
        ref="paperForm"
        label-width="100"

      >
        <t-form-item
          :label="paperType === '2' ? '作业标题' : '试卷标题'"
          name="title"
        >
          <t-input
            v-model="paperForm.title"
            :placeholder="
              '请输入' + (paperType === '2' ? '作业标题' : '试卷标题')
            "
            :maxlength="30"
            show-limit-number

          ></t-input>
        </t-form-item>

        <t-form-item name="totalScore" v-if="paperType !== '2'">
          <span>总分：{{ paperForm.totalScore }}</span>
          <span style="margin-left: 20px"
            >题目数量：{{ selectedQuestions.length }}</span
          >
          <t-link
            theme="primary"
            class="settings-link"
            @click="toggleScoreSettings"
          >
            {{ showScoreSettings ? '关闭分值设置' : '分值设置' }}
          </t-link>
          <t-link
            theme="primary"
            class="settings-link"
            @click="toggleQuestionSort"
          >
            {{ showQuestionSort ? '关闭题目排序' : '题目排序' }}
          </t-link>
          <div class="action-buttons">
            <t-button @click="handleContinueSelect" style="margin-right: 10px"
              >继续选题</t-button
            >
            <t-button theme="primary" @click="submitForm">保存</t-button>
          </div>
        </t-form-item>

        <t-form-item
          label="题目数量"
          name="questionCount"
          v-if="paperType === '2'"
        >
          <span>{{ selectedQuestions.length }}</span>
          <t-link
            theme="primary"
            class="settings-link"
            @click="toggleQuestionSort"
          >
            {{ showQuestionSort ? '关闭题目排序' : '题目排序' }}
          </t-link>
          <div class="action-buttons">
            <t-button @click="handleContinueSelect" style="margin-right: 10px"
              >继续选题</t-button
            >
            <t-button theme="primary" @click="submitForm">保存</t-button>
          </div>
        </t-form-item>

        <t-card class="questions-list">
          <div
            v-for="(question, index) in selectedQuestions"
            :key="index"
            class="question-item"
          >
            <div class="question-header">
     
              <span class="question-type"
                >({{ getQuestionTypeName(question.questionType) }})</span
              >
              <span class="question-score" v-if="paperType !== '2'"
                >({{ question.questionScore || 0 }}分)</span
              >
              <t-button
                theme="danger"
                @click="removeQuestionInStep2(index)"
                class="remove-btn"
              >
                <template #icon><t-icon name="delete" /></template>移除
              </t-button>
            </div>
            <QuestionPreview :question="question" />
            <t-divider v-if="index !== selectedQuestions.length - 1" />
          </div>
        </t-card>

      </t-form>
    </div>

    <div class="settings-panel" v-if="showScoreSettings">
      <score-settings
        :questions="selectedQuestions"
        :collections="collections"
        @score-change="handleScoreChange"
      />
    </div>

    <div class="settings-panel" v-if="showQuestionSort">
      <question-sort
        v-model:collections="collections"
        @update:collections="handleCollectionsSort"
      />
    </div>
  </div>
</template>

<script>
import QuestionFolder from '../step/questionFolder.vue'
import SelectedQuestions from '../step/selectedQuestions.vue'
import QuestionPreview from '../QuestionPreview/index.vue'
import ScoreSettings from '../step/scoreSettings.vue'
import QuestionSort from '../step/questionSort.vue'
import { listUserQuestionWithOptions } from '@/api/book/userQuestion'
import { listBookQuestionWithOptions } from '@/api/book/bookQuestion'
import {
  addPaper,
  updatePaper,
  getPaper,
  getTestPaperQuestions,
} from '@/api/book/paper'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import { questionTypeMap, questionTypeOptions } from '@/utils/quetions-utils'

export default {
  name: 'PaperSteps',
  components: {
    QuestionFolder,
    SelectedQuestions,
    QuestionPreview,
    ScoreSettings,
    QuestionSort,
  },
  props: {
    type: {
      type: String,
      default: 'add',
    },
    paperId: {
      type: String,
      default: '',
    },
    paperType: {
      type: String,
      default: '',
    },
    // 是否为嵌入模式（针对update组件的弹窗使用场景）
    isEmbedded: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update', 'save', 'step-change'],
  data() {
    return {
      currentStep: 1,
      selectedQuestions: [],
      selectedQuestionIds: [],
      collections: [],
      previewData: {
        questions: [],
      },
      showScoreSettings: false,
      showQuestionSort: false,
      paperForm: {
        title: '',
        totalScore: 0,
      },
      rules: {
        title: [
          { required: true, message: '请输入试卷标题', trigger: 'blur' },
        ],
      },
      filterForm: {
        questionType: '',
        keyword: '',
      },
      questionTypes: questionTypeOptions.filter(type => type.value !== 8),
      filteredQuestions: [],
    }
  },
  created() {
    this.initializeData()
  },
  methods: {
    initializeData() {
      // 清空之前的数据

      this.selectedQuestions = []
      this.selectedQuestionIds = []
      this.collections = []
      this.previewData.questions = []

      if (this.type === 'edit' && this.paperId) {
        this.currentStep = 2
        this.loadPaperData()
      } else {
        this.currentStep = 1
      }
    },
    resetData() {
      this.currentStep = 1
      this.selectedQuestions = []
      this.selectedQuestionIds = []
      this.collections = []
      this.paperForm = {
        title: '',
        totalScore: 0,
      }
      this.previewData = {
        questions: [],
      }
      this.filteredQuestions = []
    },
    async handleFolderSelect(folder) {
      try {
        this.previewData.questions = []

        const query = {
          folderId: folder.folderId,
          bookId: folder.bookId,
          pageSize:99999,
        }

        let response
        if (folder.type === 'book') {
          response = await listBookQuestionWithOptions(query)
        } else {
          response = await listUserQuestionWithOptions(query)
        }

    

        if (response.code === 200) {
          const questions = Array.isArray(response.rows)
            ? response.rows
                .filter(q => q.questionType != '8') // 过滤掉编程题
                .map((q) => ({
                ...q,
                selected: this.selectedQuestionIds.includes(q.questionId),
                options: q.options ? q.options.map((opt) => ({ ...opt })) : [],
              }))
            : []

          this.$nextTick(() => {
            this.previewData.questions = questions
            // 初始化 filteredQuestions 为所有题目
            this.filteredQuestions = [...questions]
          })
        } else {
          this.showError('获取题目列表失败')
        }
      } catch (error) {
        console.error('获取题目列表出错：', error)
        this.showError('获取题目列表失败')
      }
    },
    removeQuestion(index) {
      const removedQuestion = this.selectedQuestions[index]

      // 从试题篮移除
      this.selectedQuestions.splice(index, 1)

      // 从 selectedQuestionIds 移除
      const idIndex = this.selectedQuestionIds.indexOf(
        removedQuestion.questionId,
      )
      if (idIndex > -1) {
        this.selectedQuestionIds.splice(idIndex, 1)
      }

      // 从 collections 中移除
      const collection = this.collections.find(
        (c) => c.questionType === removedQuestion.questionType,
      )
      if (collection) {
        const questionIndex = collection.questions.findIndex(
          (q) => q.questionId === removedQuestion.questionId,
        )
        if (questionIndex > -1) {
          collection.questions.splice(questionIndex, 1)
          // 如果该题型的题目为空，则移除整个 collection
          if (collection.questions.length === 0) {
            const collectionIndex = this.collections.findIndex(
              (c) => c.questionType === removedQuestion.questionType,
            )
            if (collectionIndex > -1) {
              this.collections.splice(collectionIndex, 1)
            }
          }
        }
      }

      // 更新预览列表中的选中状态
      this.previewData.questions = this.previewData.questions.map((q) => ({
        ...q,
        selected: this.selectedQuestionIds.includes(q.questionId),
      }))

      // 同时更新过滤后的题目列表中的选中状态
      this.filteredQuestions = this.filteredQuestions.map((q) => ({
        ...q,
        selected: this.selectedQuestionIds.includes(q.questionId),
      }))
    },
    startGenerate() {
      this.currentStep = 2
      this.$emit('step-change', this.currentStep)
      // 更新总分
      this.paperForm.totalScore = this.selectedQuestions.reduce(
        (total, q) => total + (q.questionScore || 0),
        0,
      )
    },
    handleContinueSelect() {
      this.currentStep = 1
      this.$emit('step-change', this.currentStep)
    },
    getQuestionTypeName(type) {
      return questionTypeMap[type] || '未知题型'
    },
    async loadPaperData() {
      try {
        // 获取试卷基本信息
        const paperResponse = await getPaper(this.paperId)
        if (paperResponse.code === 200) {
          const paperData = paperResponse.data

          // 设置表单数据
          this.paperForm.title = paperData.paperTitle
          this.paperForm.totalScore = paperData.totalScore

          // 获取试卷题目详细信息
          const questionsResponse = await getTestPaperQuestions(this.paperId)
          if (questionsResponse.code === 200) {
            const collections = questionsResponse.data || []

            // 处理每个collection中的题目数据
            this.collections = collections.map((collection) => {
              // 确保每个题目都有完整的数据结构
              const questions = (collection.questionList || []).map(
                (question) => {
                  const content = question.questionContent || {}
                  return {
                    ...question,
                    selected: true,
                    questionContent: content.questionContent || '',
                    questionId: content.questionId,
                    questionType: content.questionType,
                    options: content.options || [],
                    answer: content.rightAnswer || '',
                    rightAnswer: content.rightAnswer || '',
                    analysis: content.analysis || '',
                    questionScore: question.questionScore || 0,
                  }
                },
              )

              return {
                questionType: collection.questionType,
                sort: collection.sort,
                questions: questions,
              }
            })

            // 更新已选题目列表
            this.selectedQuestions = this.collections.reduce(
              (acc, collection) => {
                return acc.concat(collection.questions)
              },
              [],
            )

            // 更新已选题目ID列表
            this.selectedQuestionIds = this.selectedQuestions.map(
              (q) => q.questionId,
            )
          }
        } else {
          this.showError('加载试卷数据失败')
        }
      } catch (error) {
        console.error('加载试卷数据失败：', error)
        this.showError('加载试卷数据失败')
      }
    },
    goToQuestionBank() {
      if (this.isEmbedded) {
        // 如果是嵌入模式，触发事件让父组件处理
        this.$emit('go-to-question-bank')
      } else {
        // 否则直接导航
        this.$router.push('/resourceLibrary/userQuestion')
      }
    },
    handleQuestionSelect(selected, question) {



      if (selected) {
        // 创建新的题目对象
        const newQuestion = {
          ...JSON.parse(JSON.stringify(question)), // 使用深拷贝而不是简单的浅拷贝
          selected: true,
          questionScore: 1,
        }

        // 添加到扁平数组
        this.selectedQuestions.push(newQuestion)
        this.selectedQuestionIds.push(question.questionId)

        // 查找或创建对应题型的 collection
        let collection = this.collections.find(
          (c) => c.questionType === question.questionType,
        )
        if (!collection) {
          collection = {
            questionType: question.questionType,
            sort: this.collections.length + 1, // 使用collections的长度+1作为排序，保证按第一次出现顺序
            questions: [],
          }
          this.collections.push(collection)
          // 移除按题型排序的代码，保持原有顺序
          // this.collections.sort((a, b) => a.questionType - b.questionType)
        }
        collection.questions.push(newQuestion)
      } else {
        // 移除逻辑保持不变
        const index = this.selectedQuestions.findIndex(
          (q) => q.questionId === question.questionId,
        )
        if (index > -1) {
          this.selectedQuestions.splice(index, 1)
        }

        const idIndex = this.selectedQuestionIds.indexOf(question.questionId)
        if (idIndex > -1) {
          this.selectedQuestionIds.splice(idIndex, 1)
        }

        // 从对应的 collection 中移除
        const collection = this.collections.find(
          (c) => c.questionType === question.questionType,
        )
        if (collection) {
          const questionIndex = collection.questions.findIndex(
            (q) => q.questionId === question.questionId,
          )
          if (questionIndex > -1) {
            collection.questions.splice(questionIndex, 1)
            // 如果该题型的题目为空，则移除整个 collection
            if (collection.questions.length === 0) {
              const collectionIndex = this.collections.findIndex(
                (c) => c.questionType === question.questionType,
              )
              if (collectionIndex > -1) {
                this.collections.splice(collectionIndex, 1)
              }
            }
          }
        }
      }

      // 更新预览列表中题目的选中状态
      this.previewData.questions = this.previewData.questions.map((q) => ({
        ...q,
        selected: this.selectedQuestionIds.includes(q.questionId),
      }))
    },
    clearAllQuestions() {
      // 清空已选题目数组
      this.selectedQuestions = []
      this.selectedQuestionIds = []
      this.collections = []

      // 更新预览列表中所有题目的选中状态为 false
      this.previewData.questions = this.previewData.questions.map((q) => ({
        ...q,
        selected: false,
      }))

      // 同时更新过滤后的题目列表
      this.filteredQuestions = this.filteredQuestions.map((q) => ({
        ...q,
        selected: false,
      }))
    },
    async submitForm() {
      try {
        // 添加试卷名称验证
        if (!this.paperForm.title) {
          this.showError('请输入试卷名称')
          return
        }

        // 添加试卷名称验证
        if (this.paperForm.title.length>30) {
          this.showError('试卷标题长度不能超过30个字')
          return
        }

        // 添加试题数量验证
        if (this.selectedQuestions.length === 0) {
          this.showError('请至少选择一道试题')
          return
        }
        if (this.selectedQuestions.length > 30) {
          this.showError('试题数量不能超过30道')
          return
        }

        // 校验是否包含编程题
        const hasProgrammingQuestion = this.selectedQuestions.some(q => q.questionType === '8');
        if (hasProgrammingQuestion) {
          this.showError('试卷中不能包含编程题');
          return;
        }




        // 构建提交数据
        const submitData = {
          paperTitle: this.paperForm.title,
          totalScore: this.paperForm.totalScore,
          questionQuantity: this.selectedQuestions.length,
          paperType: this.paperType,
          dtbTestPaperQuestionCollectionList: this.collections.map(
            (collection) => ({
              questionType: collection.questionType,
              sort: collection.sort,
              questionList: collection.questions.map((question) => ({
                questionId: question.questionId,
                questionScore: question.questionScore || 0,
              })),
            }),
          ),
        }

        // 如果是编辑模式，添加 paperId
        if (this.type === 'edit' && this.paperId) {
          submitData.paperId = this.paperId
        }

        // 调用保存接口
        const response = await (this.type === 'edit'
          ? updatePaper(submitData)
          : addPaper(submitData))

        if (response.code === 200) {
          this.showSuccess('保存成功')

          // 触发保存成功事件
          this.$emit('save', {
            paperForm: this.paperForm,
            collections: this.collections,
            selectedQuestions: this.selectedQuestions,
          })

          // 如果不是嵌入模式，直接返回列表页
          if (!this.isEmbedded) {
            this.$router.push('/resourceLibrary/paper')
          }
        } else {

        }
      } catch (error) {
        console.error('保存试卷失败：', error)

      }

    },
    handleScoreChange({ questionId, questionScore }) {


      const question = this.selectedQuestions.find(
        (q) => q.questionId === questionId,
      )
      if (question) {
        question.questionScore = questionScore
        // 更新总分
        this.paperForm.totalScore = this.selectedQuestions.reduce(
          (total, q) => total + (q.questionScore || 0),
          0,
        )
      }
    },
    toggleScoreSettings() {
      // 先关闭题目排序面板
      this.showQuestionSort = false
      // 延迟切换分值设置面板状态
      this.$nextTick(() => {
        this.showScoreSettings = !this.showScoreSettings
      })
    },
    toggleQuestionSort() {
      // 先关闭分值设置面板
      this.showScoreSettings = false
      // 延迟切换题目排序面板状态
      this.$nextTick(() => {
        this.showQuestionSort = !this.showQuestionSort
      })
    },
    handleCollectionsSort(newCollections) {
      this.collections = newCollections

      // 更新 selectedQuestions 数组以匹配新的排序
      this.selectedQuestions = newCollections.reduce((acc, collection) => {
        return acc.concat(collection.questions)
      }, [])
    },
    handleFilter() {
      if (!this.previewData.questions.length) {
        this.filteredQuestions = []
        return
      }

      this.filteredQuestions = this.previewData.questions.filter((q) => {
        const matchesType =
          !this.filterForm.questionType ||
          q.questionType === this.filterForm.questionType
        const matchesKeyword =
          !this.filterForm.keyword ||
          (q.questionContent &&
            q.questionContent
              .toLowerCase()
              .includes(this.filterForm.keyword.toLowerCase()))
        return matchesType && matchesKeyword
      })
    },
    showError(message) {
      MessagePlugin.error(message)
    },
    showSuccess(message) {
      MessagePlugin.success(message)
    },
    removeQuestionInStep2(index) {
      const dialog = DialogPlugin.confirm({
        header: '确认移除',
        body: '确定要移除该题目吗？',
        onConfirm: () => {
          const removedQuestion = this.selectedQuestions[index]

          // 从试题篮移除
          this.selectedQuestions.splice(index, 1)

          // 从 selectedQuestionIds 移除
          const idIndex = this.selectedQuestionIds.indexOf(
            removedQuestion.questionId,
          )
          if (idIndex > -1) {
            this.selectedQuestionIds.splice(idIndex, 1)
          }

          // 从 collections 中移除
          const collection = this.collections.find(
            (c) => c.questionType === removedQuestion.questionType,
          )
          if (collection) {
            const questionIndex = collection.questions.findIndex(
              (q) => q.questionId === removedQuestion.questionId,
            )
            if (questionIndex > -1) {
              collection.questions.splice(questionIndex, 1)
              // 如果该题型的题目为空，则移除整个 collection
              if (collection.questions.length === 0) {
                const collectionIndex = this.collections.findIndex(
                  (c) => c.questionType === removedQuestion.questionType,
                )
                if (collectionIndex > -1) {
                  this.collections.splice(collectionIndex, 1)
                }
              }
            }
          }

          // 更新总分
          this.paperForm.totalScore = this.selectedQuestions.reduce(
            (total, q) => total + (q.questionScore || 0),
            0,
          )

          // 更新预览列表中的选中状态
          this.previewData.questions = this.previewData.questions.map((q) => ({
            ...q,
            selected: this.selectedQuestionIds.includes(q.questionId),
          }))

          // 同时更新过滤后的题目列表
          this.filteredQuestions = this.filteredQuestions.map((q) => ({
            ...q,
            selected: this.selectedQuestionIds.includes(q.questionId),
          }))

          dialog.hide()
        },
      })
    },
  },
  watch: {
    currentStep(newValue, oldValue) {
      // 当从步骤2切换到步骤1时，重置题目的选中状态
      if (newValue === 1 && oldValue === 2) {
        // 重置筛选条件
        this.filterForm.questionType = null
        this.filterForm.keyword = null
        // 更新预览列表中题目的选中状态
        this.previewData.questions = this.previewData.questions.map((q) => ({
          ...q,
          selected: this.selectedQuestionIds.includes(q.questionId),
        }))
        // 更新过滤后的题目列表
        this.handleFilter()
      }
    },
  },
}
</script>

<style scoped>
.main-content {
  display: flex;
  gap: 20px;
  margin-top: 20px;
  min-width: 800px;

  .questions-list {
    flex: 1;
    overflow: visible;
    display: flex;
    flex-direction: column;

    :deep(.t-card__body) {
      overflow-y: visible;
      padding-right: 10px;
      display: flex;
      flex-direction: column;

      .filter-container {
        flex-shrink: 0;
      }

      > div:not(.filter-container) {
        flex: 1;
        overflow-y: visible;
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
    }

    .question-item {
      margin-bottom: 20px;

      .question-header {
        margin-bottom: 10px;
        display: flex;
        align-items: center;

        .question-index {
          font-weight: bold;
          margin-right: 8px;
        }

        .question-type {
          color: #666;
        }
      }
    }
  }

  > .t-card:first-child {
    width: 300px;
    overflow: visible;

    :deep(.t-card__body) {
      overflow-y: visible;
    }
  }

  > .t-card:last-child {
    width: 400px;
    min-width: 400px;
    overflow: visible;

    :deep(.t-card__body) {
      overflow-y: visible;
    }
  }
}

.paper-form {
  display: flex;
  gap: 20px;
  margin: 20px auto;
  padding: 20px;

  .form-content {
    flex: 1;
    min-width: 0;
  }

  .settings-panel {
    width: 400px;
    flex-shrink: 0;
    border-left: 1px solid #e4e7ed;
  }

  :deep(.t-form__item) {
    margin-bottom: 15px;
  }

  :deep(.t-form__item-content) {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .action-buttons {
    margin-left: auto;
    display: flex;
    gap: 10px;
  }

  .questions-list {
    margin: 20px 0;

    :deep(.t-card__body) {
      max-height: calc(100vh - 300px);
      overflow-y: auto;
      padding-right: 10px;
    }

    .question-item {
      margin-bottom: 20px;

      .question-header {
        margin-bottom: 10px;
        display: flex;
        align-items: center;

        .question-index {
          font-weight: bold;
          margin-right: 8px;
        }

        .question-type {
          color: #666;
          margin-right: 8px;
        }

        .question-score {
          color: #666;
          font-weight: 500;
          margin-right: auto; /* 将后续元素推到右侧 */
        }

        .remove-btn {
          margin-left: 10px;
        }
      }
    }
  }

  .score-settings-link {
    margin-left: 10px;
  }

  .settings-link {
    margin-left: 15px;
  }
}

.filter-container {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  padding: 0 10px;

  .filter-item {
    width: 200px;
  }
}
</style>
