<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨域诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .error-box {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .code {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        .diagnostic-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 跨域诊断工具</h1>
        <p>这个工具帮助诊断iframe跨域通信问题</p>
        
        <div class="info-box">
            <h3>📍 当前页面信息</h3>
            <p><strong>URL:</strong> <span id="currentUrl"></span></p>
            <p><strong>Origin:</strong> <span id="currentOrigin"></span></p>
            <p><strong>Protocol:</strong> <span id="currentProtocol"></span></p>
            <p><strong>Host:</strong> <span id="currentHost"></span></p>
            <p><strong>Port:</strong> <span id="currentPort"></span></p>
        </div>
        
        <div class="info-box">
            <h3>🔗 父页面信息</h3>
            <p><strong>是否在iframe中:</strong> <span id="inIframe"></span></p>
            <p><strong>父页面Origin:</strong> <span id="parentOrigin"></span></p>
            <p><strong>可以访问父页面:</strong> <span id="canAccessParent"></span></p>
        </div>
        
        <div>
            <h3>🧪 测试功能</h3>
            <button onclick="testPostMessage()">测试PostMessage</button>
            <button onclick="testParentAccess()">测试父页面访问</button>
            <button onclick="checkHeaders()">检查HTTP头部</button>
            <button onclick="runFullDiagnostic()">完整诊断</button>
        </div>
        
        <div id="diagnosticResults"></div>
        
        <div class="info-box">
            <h3>📋 常见跨域问题和解决方案</h3>
            <ul>
                <li><strong>不同端口:</strong> http://localhost:3000 和 http://localhost:81 被视为不同域</li>
                <li><strong>协议不同:</strong> http:// 和 https:// 被视为不同域</li>
                <li><strong>子域名不同:</strong> app.example.com 和 api.example.com 被视为不同域</li>
                <li><strong>X-Frame-Options:</strong> 目标页面可能禁止被嵌入iframe</li>
                <li><strong>CSP策略:</strong> Content Security Policy 可能阻止跨域加载</li>
            </ul>
        </div>
        
        <div class="code" id="solutionCode" style="display: none;">
            <h4>💡 解决方案代码:</h4>
        </div>
    </div>

    <script>
        // 页面加载时获取基本信息
        window.addEventListener('load', function() {
            updatePageInfo();
            checkIframeStatus();
            
            // 设置postMessage监听器
            window.addEventListener('message', function(event) {
                logMessage('收到消息', {
                    origin: event.origin,
                    data: event.data,
                    source: event.source === window.parent ? 'parent' : 'other'
                });
            });
            
            // 自动发送ready消息
            setTimeout(() => {
                sendReadyMessage();
            }, 1000);
        });
        
        function updatePageInfo() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('currentOrigin').textContent = window.location.origin;
            document.getElementById('currentProtocol').textContent = window.location.protocol;
            document.getElementById('currentHost').textContent = window.location.hostname;
            document.getElementById('currentPort').textContent = window.location.port || '默认端口';
        }
        
        function checkIframeStatus() {
            const inIframe = window !== window.parent;
            document.getElementById('inIframe').textContent = inIframe ? '是' : '否';
            
            if (inIframe) {
                try {
                    const parentOrigin = document.referrer ? new URL(document.referrer).origin : '未知';
                    document.getElementById('parentOrigin').textContent = parentOrigin;
                } catch (e) {
                    document.getElementById('parentOrigin').textContent = '无法获取';
                }
                
                try {
                    const canAccess = !!window.parent.location.href;
                    document.getElementById('canAccessParent').textContent = '是';
                } catch (e) {
                    document.getElementById('canAccessParent').textContent = '否 (跨域限制)';
                }
            } else {
                document.getElementById('parentOrigin').textContent = '不在iframe中';
                document.getElementById('canAccessParent').textContent = '不适用';
            }
        }
        
        function testPostMessage() {
            if (window !== window.parent) {
                try {
                    window.parent.postMessage({
                        type: 'test_message',
                        message: '这是一条测试消息',
                        timestamp: new Date().toISOString(),
                        from: window.location.origin
                    }, '*');
                    
                    addResult('success', 'PostMessage测试', 'PostMessage发送成功');
                } catch (error) {
                    addResult('error', 'PostMessage测试', 'PostMessage发送失败: ' + error.message);
                }
            } else {
                addResult('info', 'PostMessage测试', '当前不在iframe中，无法测试');
            }
        }
        
        function testParentAccess() {
            if (window !== window.parent) {
                try {
                    const parentUrl = window.parent.location.href;
                    addResult('success', '父页面访问测试', '可以访问父页面URL: ' + parentUrl);
                } catch (error) {
                    addResult('error', '父页面访问测试', '无法访问父页面: ' + error.message + ' (这是正常的跨域保护)');
                }
            } else {
                addResult('info', '父页面访问测试', '当前不在iframe中，无法测试');
            }
        }
        
        function checkHeaders() {
            fetch(window.location.href)
                .then(response => {
                    const headers = {};
                    for (let [key, value] of response.headers.entries()) {
                        headers[key] = value;
                    }
                    
                    let headerInfo = '响应头信息:\n';
                    headerInfo += JSON.stringify(headers, null, 2);
                    
                    // 检查关键的跨域相关头部
                    if (headers['x-frame-options']) {
                        headerInfo += '\n\n⚠️ 发现 X-Frame-Options: ' + headers['x-frame-options'];
                        headerInfo += '\n这可能阻止页面被嵌入iframe';
                    }
                    
                    if (headers['content-security-policy']) {
                        headerInfo += '\n\n⚠️ 发现 Content-Security-Policy: ' + headers['content-security-policy'];
                        headerInfo += '\n这可能影响跨域加载';
                    }
                    
                    addResult('info', 'HTTP头部检查', headerInfo);
                })
                .catch(error => {
                    addResult('error', 'HTTP头部检查', '无法获取头部信息: ' + error.message);
                });
        }
        
        function runFullDiagnostic() {
            clearResults();
            
            // 基本信息诊断
            addResult('info', '环境诊断', `
当前页面: ${window.location.href}
父页面: ${window !== window.parent ? '在iframe中' : '独立页面'}
用户代理: ${navigator.userAgent}
            `);
            
            // 运行所有测试
            testPostMessage();
            testParentAccess();
            checkHeaders();
            
            // 提供解决方案
            setTimeout(() => {
                provideSolutions();
            }, 1000);
        }
        
        function provideSolutions() {
            const solutionCode = document.getElementById('solutionCode');
            solutionCode.style.display = 'block';
            
            let solutions = `
// 1. 在阅读器页面中添加postMessage支持
window.addEventListener('message', function(event) {
    // 验证来源
    const allowedOrigins = [
        'http://localhost:3000',
        'http://localhost:8080',
        'http://localhost:81',
        '${window.location.origin}'
    ];
    
    if (!allowedOrigins.includes(event.origin)) {
        console.warn('未授权的消息来源:', event.origin);
        return;
    }
    
    console.log('收到父页面消息:', event.data);
    
    // 处理消息
    if (event.data.type === 'parent_ready') {
        // 回复ready消息
        window.parent.postMessage({
            type: 'iframe_ready',
            message: 'iframe已准备就绪'
        }, event.origin);
    }
});

// 2. 页面加载完成后发送ready消息
window.addEventListener('load', function() {
    setTimeout(() => {
        window.parent.postMessage({
            type: 'iframe_ready',
            message: 'iframe页面加载完成'
        }, '*');
    }, 1000);
});

// 3. 如果遇到X-Frame-Options问题，需要在服务器端设置:
// X-Frame-Options: SAMEORIGIN
// 或者
// X-Frame-Options: ALLOW-FROM ${window.location.origin}
            `;
            
            solutionCode.innerHTML = '<h4>💡 解决方案代码:</h4>' + solutions;
        }
        
        function sendReadyMessage() {
            if (window !== window.parent) {
                window.parent.postMessage({
                    type: 'iframe_ready',
                    message: 'iframe已准备就绪',
                    timestamp: new Date().toISOString(),
                    origin: window.location.origin
                }, '*');
                
                logMessage('发送', {
                    type: 'iframe_ready',
                    message: 'iframe已准备就绪'
                });
            }
        }
        
        function addResult(type, title, message) {
            const resultsDiv = document.getElementById('diagnosticResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `diagnostic-result ${type}-box`;
            resultDiv.innerHTML = `<h4>${title}</h4><pre>${message}</pre>`;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('diagnosticResults').innerHTML = '';
        }
        
        function logMessage(type, data) {
            console.log(`[${type}]`, data);
            addResult('info', `消息${type}`, JSON.stringify(data, null, 2));
        }
    </script>
</body>
</html>
