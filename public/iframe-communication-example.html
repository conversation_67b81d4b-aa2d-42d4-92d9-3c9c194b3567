<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iframe通信示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .message-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .message-item {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .message-received {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        .message-sent {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>iframe PostMessage 通信示例</h1>
        <p>这个页面演示了如何在iframe中与父页面进行postMessage通信</p>
        
        <div id="status" class="status disconnected">
            状态: 等待连接...
        </div>
        
        <div>
            <h3>发送消息到父页面:</h3>
            <button onclick="sendReadyMessage()">发送就绪消息</button>
            <button onclick="sendContentLoaded()">发送内容加载完成</button>
            <button onclick="sendUserInteraction()">发送用户交互</button>
            <button onclick="sendErrorMessage()">发送错误消息</button>
        </div>
        
        <div>
            <h3>消息日志:</h3>
            <div id="messageLog" class="message-log">
                <div class="message-item">等待消息...</div>
            </div>
        </div>
        
        <div>
            <h3>接收到的数据:</h3>
            <pre id="receivedData">暂无数据</pre>
        </div>
    </div>

    <script>
        let parentOrigin = null;
        let isConnected = false;
        
        // 设置消息监听器
        window.addEventListener('message', handleParentMessage, false);
        
        // 页面加载完成后发送就绪消息
        window.addEventListener('load', function() {
            console.log('iframe页面加载完成');
            // 尝试向可能的父页面发送就绪消息
            sendMessageToParent({
                type: 'iframe_ready',
                message: 'iframe已准备就绪',
                timestamp: new Date().toISOString()
            });
        });
        
        // 处理来自父页面的消息
        function handleParentMessage(event) {
            console.log('收到父页面消息:', event);
            
            // 记录父页面的origin
            if (!parentOrigin) {
                parentOrigin = event.origin;
                updateConnectionStatus(true);
            }
            
            // 验证消息来源（在实际应用中应该更严格）
            const allowedOrigins = [
                'http://localhost:3000',
                'http://localhost:8080',
                'http://localhost:81',
                'https://ebook.dutp.cn'
            ];
            
            if (!allowedOrigins.includes(event.origin)) {
                console.warn('收到来自未授权域的消息:', event.origin);
                return;
            }
            
            logMessage('received', `来自 ${event.origin}: ${JSON.stringify(event.data)}`);
            
            // 处理不同类型的消息
            switch (event.data.type) {
                case 'parent_ready':
                    console.log('父页面已准备就绪');
                    updateReceivedData(event.data);
                    break;
                    
                case 'init_data':
                    console.log('收到初始化数据:', event.data);
                    updateReceivedData(event.data);
                    break;
                    
                case 'update_content':
                    console.log('更新内容:', event.data);
                    updateReceivedData(event.data);
                    break;
                    
                default:
                    console.log('未知消息类型:', event.data.type);
                    updateReceivedData(event.data);
            }
        }
        
        // 向父页面发送消息
        function sendMessageToParent(data) {
            try {
                // 如果知道父页面的origin，使用具体的origin，否则使用'*'
                const targetOrigin = parentOrigin || '*';
                window.parent.postMessage(data, targetOrigin);
                logMessage('sent', `发送到父页面: ${JSON.stringify(data)}`);
                console.log('向父页面发送消息:', data);
            } catch (error) {
                console.error('发送消息到父页面失败:', error);
                logMessage('sent', `发送失败: ${error.message}`);
            }
        }
        
        // 更新连接状态
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusEl = document.getElementById('status');
            if (connected) {
                statusEl.textContent = `状态: 已连接到 ${parentOrigin}`;
                statusEl.className = 'status connected';
            } else {
                statusEl.textContent = '状态: 未连接';
                statusEl.className = 'status disconnected';
            }
        }
        
        // 记录消息
        function logMessage(type, message) {
            const logEl = document.getElementById('messageLog');
            const messageEl = document.createElement('div');
            messageEl.className = `message-item message-${type}`;
            messageEl.innerHTML = `
                <strong>${new Date().toLocaleTimeString()}</strong><br>
                ${message}
            `;
            logEl.appendChild(messageEl);
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        // 更新接收到的数据显示
        function updateReceivedData(data) {
            const dataEl = document.getElementById('receivedData');
            dataEl.textContent = JSON.stringify(data, null, 2);
        }
        
        // 发送各种类型的消息
        function sendReadyMessage() {
            sendMessageToParent({
                type: 'iframe_ready',
                message: 'iframe已准备就绪',
                timestamp: new Date().toISOString()
            });
        }
        
        function sendContentLoaded() {
            sendMessageToParent({
                type: 'content_loaded',
                message: '内容加载完成',
                timestamp: new Date().toISOString()
            });
        }
        
        function sendUserInteraction() {
            sendMessageToParent({
                type: 'user_interaction',
                action: 'button_click',
                message: '用户点击了按钮',
                timestamp: new Date().toISOString()
            });
        }
        
        function sendErrorMessage() {
            sendMessageToParent({
                type: 'iframe_error',
                error: '这是一个测试错误消息',
                timestamp: new Date().toISOString()
            });
        }
        
        // 模拟定期发送心跳消息
        setInterval(() => {
            if (isConnected) {
                sendMessageToParent({
                    type: 'heartbeat',
                    timestamp: new Date().toISOString()
                });
            }
        }, 30000); // 每30秒发送一次心跳
    </script>
</body>
</html>
